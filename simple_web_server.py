#!/usr/bin/env python3
"""
Debug Agent - Serveur Web Simple
Version simplifiée sans dépendances externes

Auteur: Assistant IA
Date: 2025-01-14
"""

import http.server
import socketserver
import json
import os
import urllib.parse
import tempfile
import shutil
from datetime import datetime
import threading
import webbrowser
import time

# Import des modules d'analyse
try:
    from fred2000_debug_agent import FRED2000DebugAgent
    from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
    from report_generator import FRED2000ReportGenerator
    ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modules d'analyse non disponibles: {e}")
    ANALYSIS_AVAILABLE = False

class FRED2000Handler(http.server.SimpleHTTPRequestHandler):
    """Handler HTTP personnalisé pour FRED2000"""
    
    def __init__(self, *args, **kwargs):
        # Créer les dossiers nécessaires
        os.makedirs('uploads', exist_ok=True)
        os.makedirs('reports', exist_ok=True)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Gestion des requêtes GET"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path.startswith('/api/'):
            self.handle_api_get()
        else:
            super().do_GET()
    
    def do_POST(self):
        """Gestion des requêtes POST"""
        if self.path.startswith('/api/'):
            self.handle_api_post()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale"""
        html_content = self.get_main_html()
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-length', len(html_content.encode('utf-8')))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def handle_api_get(self):
        """Gestion des API GET"""
        if self.path == '/api/health':
            self.send_json_response({
                'status': 'healthy',
                'analysis_available': ANALYSIS_AVAILABLE,
                'timestamp': datetime.now().isoformat()
            })
        else:
            self.send_error(404)
    
    def handle_api_post(self):
        """Gestion des API POST"""
        if self.path == '/api/upload':
            self.handle_upload()
        elif self.path == '/api/analyze':
            self.handle_analyze()
        else:
            self.send_error(404)
    
    def handle_upload(self):
        """Gestion de l'upload de fichiers"""
        try:
            content_type = self.headers.get('Content-Type', '')
            if not content_type.startswith('multipart/form-data'):
                self.send_json_response({'error': 'Format non supporté'}, 400)
                return
            
            # Pour cette version simplifiée, on simule l'upload
            # Dans une vraie implémentation, il faudrait parser le multipart
            
            # Chercher les fichiers .ec existants dans le dossier courant
            ec_files = []
            for file in os.listdir('.'):
                if file.lower().endswith('.ec'):
                    size = os.path.getsize(file)
                    ec_files.append({
                        'name': file,
                        'path': os.path.abspath(file),
                        'size': size,
                        'type': 'ec'
                    })
            
            session_id = 'demo-session'
            
            response = {
                'session_id': session_id,
                'files': ec_files,
                'message': f'{len(ec_files)} fichier(s) .ec détecté(s) dans le dossier courant'
            }
            
            self.send_json_response(response)
            
        except Exception as e:
            self.send_json_response({'error': f'Erreur upload: {str(e)}'}, 500)
    
    def handle_analyze(self):
        """Gestion de l'analyse"""
        try:
            if not ANALYSIS_AVAILABLE:
                self.send_json_response({
                    'error': 'Modules d\'analyse non disponibles. Vérifiez l\'installation.'
                }, 500)
                return
            
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            selected_files = data.get('files', [])
            options = data.get('options', {})
            
            if not selected_files:
                self.send_json_response({'error': 'Aucun fichier sélectionné'}, 400)
                return
            
            # Lancer l'analyse dans un thread séparé
            analysis_id = 'demo-analysis'
            
            def run_analysis():
                try:
                    results = {}
                    
                    for file_name in selected_files:
                        if os.path.exists(file_name):
                            print(f"📄 Analyse de {file_name}...")
                            file_results = self.analyze_single_file(file_name, options)
                            results[file_name] = file_results
                    
                    # Sauvegarder les résultats
                    with open('last_analysis_results.json', 'w', encoding='utf-8') as f:
                        json.dump(results, f, indent=2, ensure_ascii=False)
                    
                    print(f"✅ Analyse terminée: {len(results)} fichiers analysés")
                    
                except Exception as e:
                    print(f"❌ Erreur lors de l'analyse: {e}")
            
            # Lancer l'analyse
            thread = threading.Thread(target=run_analysis)
            thread.daemon = True
            thread.start()
            
            self.send_json_response({
                'analysis_id': analysis_id,
                'message': 'Analyse démarrée (vérifiez la console)',
                'status': 'starting'
            })
            
        except Exception as e:
            self.send_json_response({'error': f'Erreur analyse: {str(e)}'}, 500)
    
    def analyze_single_file(self, file_path, options):
        """Analyse un seul fichier"""
        results = {
            'file_path': file_path,
            'issues': [],
            'stats': {},
            'analysis_types': []
        }
        
        try:
            # Créer l'agent pour ce fichier
            agent = FRED2000DebugAgent(file_path)
            
            # Statistiques de base
            results['stats'] = {
                'total_lines': len(agent.code_lines),
                'total_functions': len(agent.functions),
                'file_size': os.path.getsize(file_path)
            }
            
            all_issues = []
            
            # Analyses selon les options
            if options.get('sql_analysis', True):
                sql_detector = SQLErrorDetector()
                sql_issues = sql_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(sql_issues)
                results['analysis_types'].append('SQL')
            
            if options.get('memory_analysis', True):
                memory_detector = MemoryErrorDetector()
                memory_issues = memory_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(memory_issues)
                results['analysis_types'].append('Mémoire')
            
            if options.get('business_analysis', True):
                business_detector = BusinessLogicDetector()
                business_issues = business_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(business_issues)
                results['analysis_types'].append('Métier')
            
            if options.get('performance_analysis', True):
                performance_detector = PerformanceDetector()
                performance_issues = performance_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(performance_issues)
                results['analysis_types'].append('Performance')
            
            # Convertir les issues en dictionnaires
            results['issues'] = [
                {
                    'type': issue.error_type.value,
                    'severity': issue.severity.value,
                    'line': issue.line_number,
                    'function': issue.function_name,
                    'description': issue.description,
                    'code': issue.code_snippet,
                    'suggestion': issue.suggestion
                }
                for issue in all_issues
            ]
            
        except Exception as e:
            results['error'] = str(e)
        
        return results
    
    def send_json_response(self, data, status_code=200):
        """Envoie une réponse JSON"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-length', len(json_data.encode('utf-8')))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_main_html(self):
        """Retourne le HTML de la page principale"""
        return '''<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Agent - Version Simple</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer; font-size: 16px; font-weight: 600; margin: 5px; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .status { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .file-list { margin: 20px 0; }
        .file-item { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; display: flex; justify-content: space-between; align-items: center; }
        .options { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .option { display: flex; align-items: center; gap: 10px; }
        .results { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; max-height: 400px; overflow-y: auto; }
        .hidden { display: none; }
        input[type="checkbox"] { transform: scale(1.2); }
        textarea { width: 100%; height: 300px; font-family: monospace; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Debug Agent</h1>
            <p>Version Simple - Interface Web</p>
        </div>

        <div class="card">
            <h2>📄 Fichiers .ec Détectés</h2>
            <p>Cette version détecte automatiquement les fichiers .ec dans le dossier courant.</p>
            <button class="btn" onclick="detectFiles()">🔍 Détecter les fichiers</button>
            <div id="fileStatus" class="status hidden"></div>
            <div id="fileList" class="file-list hidden"></div>
        </div>

        <div class="card">
            <h2>⚙️ Options d'Analyse</h2>
            <div class="options">
                <div class="option">
                    <input type="checkbox" id="sqlAnalysis" checked>
                    <label>🗄️ Analyse SQL/ESQL</label>
                </div>
                <div class="option">
                    <input type="checkbox" id="memoryAnalysis" checked>
                    <label>🧠 Analyse Mémoire</label>
                </div>
                <div class="option">
                    <input type="checkbox" id="businessAnalysis" checked>
                    <label>📊 Logique Métier</label>
                </div>
                <div class="option">
                    <input type="checkbox" id="performanceAnalysis" checked>
                    <label>⚡ Performance</label>
                </div>
            </div>
            <button class="btn" onclick="startAnalysis()" id="analyzeBtn" disabled>🚀 Démarrer l'Analyse</button>
            <div id="analysisStatus" class="status hidden"></div>
        </div>

        <div class="card">
            <h2>📊 Résultats</h2>
            <p>Les résultats d'analyse apparaîtront ici et dans la console du serveur.</p>
            <button class="btn" onclick="loadResults()">🔄 Charger les derniers résultats</button>
            <div id="results" class="results hidden">
                <textarea id="resultsText" readonly placeholder="Aucun résultat disponible..."></textarea>
            </div>
        </div>

        <div class="card">
            <h2>ℹ️ Instructions</h2>
            <ol style="line-height: 1.8; margin-left: 20px;">
                <li>Placez vos fichiers .ec dans le même dossier que ce serveur</li>
                <li>Cliquez sur "Détecter les fichiers" pour les trouver</li>
                <li>Sélectionnez les options d'analyse souhaitées</li>
                <li>Cliquez sur "Démarrer l'Analyse"</li>
                <li>Consultez les résultats dans la console et ici</li>
            </ol>
        </div>
    </div>

    <script>
        let sessionId = null;
        let detectedFiles = [];

        async function detectFiles() {
            try {
                showStatus('fileStatus', 'Détection des fichiers...', 'info');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    headers: { 'Content-Type': 'multipart/form-data' }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    sessionId = result.session_id;
                    detectedFiles = result.files;
                    displayFiles(result.files);
                    showStatus('fileStatus', result.message, 'success');
                    document.getElementById('analyzeBtn').disabled = false;
                } else {
                    showStatus('fileStatus', result.error, 'error');
                }
            } catch (error) {
                showStatus('fileStatus', 'Erreur lors de la détection: ' + error.message, 'error');
            }
        }

        function displayFiles(files) {
            const fileList = document.getElementById('fileList');
            const html = files.map(file => `
                <div class="file-item">
                    <div>
                        <strong>${file.name}</strong><br>
                        <small>${formatFileSize(file.size)}</small>
                    </div>
                    <input type="checkbox" checked data-file="${file.name}">
                </div>
            `).join('');
            
            fileList.innerHTML = html;
            fileList.classList.remove('hidden');
        }

        async function startAnalysis() {
            try {
                const selectedFiles = Array.from(document.querySelectorAll('input[data-file]:checked'))
                    .map(cb => cb.dataset.file);
                
                if (selectedFiles.length === 0) {
                    showStatus('analysisStatus', 'Aucun fichier sélectionné', 'error');
                    return;
                }

                const options = {
                    sql_analysis: document.getElementById('sqlAnalysis').checked,
                    memory_analysis: document.getElementById('memoryAnalysis').checked,
                    business_analysis: document.getElementById('businessAnalysis').checked,
                    performance_analysis: document.getElementById('performanceAnalysis').checked
                };

                showStatus('analysisStatus', 'Démarrage de l\\'analyse...', 'info');
                document.getElementById('analyzeBtn').disabled = true;

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: sessionId,
                        files: selectedFiles,
                        options: options
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showStatus('analysisStatus', result.message + ' - Consultez la console du serveur', 'success');
                    
                    // Attendre un peu puis charger les résultats
                    setTimeout(loadResults, 3000);
                } else {
                    showStatus('analysisStatus', result.error, 'error');
                }
            } catch (error) {
                showStatus('analysisStatus', 'Erreur: ' + error.message, 'error');
            } finally {
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        async function loadResults() {
            try {
                const response = await fetch('last_analysis_results.json');
                if (response.ok) {
                    const results = await response.json();
                    displayResults(results);
                } else {
                    showStatus('analysisStatus', 'Aucun résultat disponible', 'info');
                }
            } catch (error) {
                showStatus('analysisStatus', 'Impossible de charger les résultats', 'error');
            }
        }

        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const resultsText = document.getElementById('resultsText');
            
            let text = 'RÉSULTATS D\\'ANALYSE FRED2000\\n';
            text += '================================\\n\\n';
            
            Object.entries(results).forEach(([fileName, result]) => {
                const issues = result.issues || [];
                const stats = result.stats || {};
                
                text += `📄 ${fileName}\\n`;
                text += `   Lignes: ${stats.total_lines || 'N/A'}\\n`;
                text += `   Fonctions: ${stats.total_functions || 'N/A'}\\n`;
                text += `   Problèmes: ${issues.length}\\n\\n`;
                
                if (issues.length > 0) {
                    text += '   🔍 Problèmes détectés:\\n';
                    issues.slice(0, 5).forEach((issue, i) => {
                        text += `   ${i+1}. [${issue.severity.toUpperCase()}] Ligne ${issue.line}\\n`;
                        text += `      ${issue.description}\\n`;
                        text += `      💡 ${issue.suggestion}\\n\\n`;
                    });
                    
                    if (issues.length > 5) {
                        text += `   ... et ${issues.length - 5} autres problèmes\\n\\n`;
                    }
                } else {
                    text += '   ✅ Aucun problème détecté!\\n\\n';
                }
                
                text += '-'.repeat(40) + '\\n\\n';
            });
            
            resultsText.value = text;
            resultsDiv.classList.remove('hidden');
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        // Détecter automatiquement les fichiers au chargement
        window.onload = function() {
            detectFiles();
        };
    </script>
</body>
</html>'''

def open_browser_delayed():
    """Ouvre le navigateur après un délai"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
        print("🌐 Navigateur ouvert sur http://localhost:8000")
    except Exception as e:
        print(f"⚠ Impossible d'ouvrir le navigateur: {e}")

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🔧 DEBUG AGENT - Serveur Web Simple")
    print("   Version sans dépendances externes")
    print("   Version 1.0 - 2025-01-14")
    print("=" * 60)
    print()
    
    if not ANALYSIS_AVAILABLE:
        print("⚠ Modules d'analyse non disponibles")
        print("  L'interface fonctionnera mais l'analyse sera limitée")
        print()
    
    port = 8000
    
    # Ouvrir le navigateur dans un thread séparé
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", port), FRED2000Handler) as httpd:
            print(f"🚀 Serveur démarré sur http://localhost:{port}")
            print("📱 Interface web disponible dans votre navigateur")
            print("⏹️  Appuyez sur Ctrl+C pour arrêter")
            print()
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Serveur arrêté")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
