@echo off
echo ============================================================
echo    Debug Agent - Interface Graphique
echo    Agent de Debogage Specialise pour FRED2000
echo    Version 1.0 - 2025-01-14
echo ============================================================
echo.

REM Changer vers le repertoire du script
cd /d "%~dp0"

echo Verification de Python...

REM Essayer python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python trouve!
    echo Lancement de l'interface graphique...
    python launch_fred2000_debug.py
    goto :end
)

REM Essayer python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python3 trouve!
    echo Lancement de l'interface graphique...
    python3 launch_fred2000_debug.py
    goto :end
)

REM Essayer py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python (py) trouve!
    echo Lancement de l'interface graphique...
    py launch_fred2000_debug.py
    goto :end
)

REM Python non trouve
echo.
echo ERREUR: Python n'est pas installe ou non accessible.
echo.
echo Solutions:
echo 1. Installez Python depuis https://python.org
echo 2. Ou installez depuis le Microsoft Store
echo 3. Verifiez que Python est dans le PATH
echo.
echo Appuyez sur une touche pour ouvrir le site Python...
pause >nul
start https://python.org/downloads/
goto :end

:end
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
