#!/usr/bin/env python3
"""
Générateur de rapports pour l'Agent de Débogage FRED2000
Génère des rapports en différents formats (HTML, JSON, CSV)

Auteur: Assistant IA
Date: 2025-01-14
"""

import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Any

class FRED2000ReportGenerator:
    """Générateur de rapports pour FRED2000"""
    
    def __init__(self):
        self.template_html = self._get_html_template()
    
    def generate_html_report(self, analysis_results: Dict, output_file: str = None) -> str:
        """Génère un rapport HTML"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"fred2000_report_{timestamp}.html"
        
        # Calculer les statistiques
        stats = self._calculate_statistics(analysis_results)
        
        # <PERSON><PERSON>érer le contenu HTML
        html_content = self.template_html.format(
            title="Rapport d'Analyse FRED2000",
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            total_files=stats['total_files'],
            total_issues=stats['total_issues'],
            critical_count=stats['severity_counts'].get('critical', 0),
            high_count=stats['severity_counts'].get('high', 0),
            medium_count=stats['severity_counts'].get('medium', 0),
            low_count=stats['severity_counts'].get('low', 0),
            files_details=self._generate_files_html(analysis_results),
            issues_by_type=self._generate_issues_by_type_html(stats['type_counts']),
            recommendations=self._generate_recommendations_html(stats)
        )
        
        # Écrire le fichier
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_file
    
    def generate_json_report(self, analysis_results: Dict, output_file: str = None) -> str:
        """Génère un rapport JSON"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"fred2000_report_{timestamp}.json"
        
        # Préparer les données
        report_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "tool_version": "1.0",
                "total_files": len(analysis_results),
                "total_issues": sum(len(result.get('issues', [])) for result in analysis_results.values())
            },
            "statistics": self._calculate_statistics(analysis_results),
            "files": {}
        }
        
        # Ajouter les détails par fichier
        for file_path, result in analysis_results.items():
            file_name = os.path.basename(file_path)
            report_data["files"][file_name] = {
                "path": file_path,
                "stats": result.get('stats', {}),
                "issues": result.get('issues', []),
                "analysis_types": result.get('analysis_types', [])
            }
        
        # Écrire le fichier
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return output_file
    
    def generate_csv_report(self, analysis_results: Dict, output_file: str = None) -> str:
        """Génère un rapport CSV des problèmes"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"fred2000_issues_{timestamp}.csv"
        
        # Préparer les données
        csv_data = []
        
        for file_path, result in analysis_results.items():
            file_name = os.path.basename(file_path)
            
            for issue in result.get('issues', []):
                csv_data.append({
                    'Fichier': file_name,
                    'Ligne': issue.get('line', ''),
                    'Fonction': issue.get('function', ''),
                    'Type': issue.get('type', ''),
                    'Gravité': issue.get('severity', ''),
                    'Description': issue.get('description', ''),
                    'Code': issue.get('code', ''),
                    'Suggestion': issue.get('suggestion', '')
                })
        
        # Écrire le fichier CSV
        if csv_data:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
                writer.writeheader()
                writer.writerows(csv_data)
        
        return output_file
    
    def _calculate_statistics(self, analysis_results: Dict) -> Dict:
        """Calcule les statistiques globales"""
        stats = {
            'total_files': len(analysis_results),
            'total_issues': 0,
            'severity_counts': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
            'type_counts': {},
            'files_with_issues': 0,
            'average_issues_per_file': 0
        }
        
        for result in analysis_results.values():
            issues = result.get('issues', [])
            if issues:
                stats['files_with_issues'] += 1
            
            stats['total_issues'] += len(issues)
            
            for issue in issues:
                severity = issue.get('severity', 'unknown')
                issue_type = issue.get('type', 'unknown')
                
                if severity in stats['severity_counts']:
                    stats['severity_counts'][severity] += 1
                
                stats['type_counts'][issue_type] = stats['type_counts'].get(issue_type, 0) + 1
        
        if stats['total_files'] > 0:
            stats['average_issues_per_file'] = stats['total_issues'] / stats['total_files']
        
        return stats
    
    def _generate_files_html(self, analysis_results: Dict) -> str:
        """Génère le HTML pour les détails des fichiers"""
        html_parts = []
        
        for file_path, result in analysis_results.items():
            file_name = os.path.basename(file_path)
            issues = result.get('issues', [])
            stats = result.get('stats', {})
            
            # Couleur selon le nombre de problèmes
            if len(issues) == 0:
                status_class = "success"
                status_text = "✅ Aucun problème"
            elif len(issues) < 5:
                status_class = "warning"
                status_text = f"⚠ {len(issues)} problème(s)"
            else:
                status_class = "danger"
                status_text = f"🚨 {len(issues)} problème(s)"
            
            html_parts.append(f"""
            <div class="file-card">
                <h3>📄 {file_name}</h3>
                <div class="file-stats">
                    <span class="stat">Lignes: {stats.get('total_lines', 'N/A')}</span>
                    <span class="stat">Fonctions: {stats.get('total_functions', 'N/A')}</span>
                    <span class="stat status-{status_class}">{status_text}</span>
                </div>
            """)
            
            if issues:
                html_parts.append("<div class='issues-list'>")
                for i, issue in enumerate(issues[:5], 1):  # Top 5
                    severity_class = issue.get('severity', 'low')
                    html_parts.append(f"""
                    <div class="issue-item severity-{severity_class}">
                        <strong>Ligne {issue.get('line', 'N/A')}</strong> - {issue.get('description', '')}
                        <br><small>💡 {issue.get('suggestion', '')}</small>
                    </div>
                    """)
                
                if len(issues) > 5:
                    html_parts.append(f"<div class='more-issues'>... et {len(issues) - 5} autres problèmes</div>")
                
                html_parts.append("</div>")
            
            html_parts.append("</div>")
        
        return "\n".join(html_parts)
    
    def _generate_issues_by_type_html(self, type_counts: Dict) -> str:
        """Génère le HTML pour les problèmes par type"""
        html_parts = []
        
        for issue_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            type_name = issue_type.replace('_', ' ').title()
            html_parts.append(f"""
            <div class="type-item">
                <span class="type-name">{type_name}</span>
                <span class="type-count">{count}</span>
            </div>
            """)
        
        return "\n".join(html_parts)
    
    def _generate_recommendations_html(self, stats: Dict) -> str:
        """Génère les recommandations HTML"""
        recommendations = []
        
        total_issues = stats['total_issues']
        severity_counts = stats['severity_counts']
        
        if total_issues == 0:
            recommendations.append("✅ Excellent! Aucun problème détecté.")
        elif total_issues < 10:
            recommendations.append("🟢 Bon état général. Quelques améliorations mineures possibles.")
        elif total_issues < 50:
            recommendations.append("🟡 État correct. Recommandé de corriger les problèmes de haute priorité.")
        else:
            recommendations.append("🔴 Attention! Nombreux problèmes détectés. Révision approfondie recommandée.")
        
        if severity_counts['critical'] > 0:
            recommendations.append(f"🚨 {severity_counts['critical']} problème(s) critique(s) à corriger en priorité!")
        
        if 'sql_error' in stats['type_counts']:
            recommendations.append("🗄️ Vérifiez la gestion d'erreurs SQL et les transactions.")
        
        if 'buffer_overflow' in stats['type_counts']:
            recommendations.append("🛡️ Attention aux fonctions dangereuses (strcpy, sprintf, etc.).")
        
        if 'performance' in stats['type_counts']:
            recommendations.append("⚡ Optimisez les boucles contenant des requêtes SQL.")
        
        return "<br>".join(recommendations)
    
    def _get_html_template(self) -> str:
        """Retourne le template HTML"""
        return """<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        .header-info {{ background: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 30px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .stat-card {{ background: #3498db; color: white; padding: 20px; border-radius: 8px; text-align: center; }}
        .stat-card.critical {{ background: #e74c3c; }}
        .stat-card.high {{ background: #f39c12; }}
        .stat-card.medium {{ background: #f1c40f; color: #2c3e50; }}
        .stat-card.low {{ background: #27ae60; }}
        .stat-number {{ font-size: 2em; font-weight: bold; display: block; }}
        .stat-label {{ font-size: 0.9em; opacity: 0.9; }}
        .section {{ margin-bottom: 40px; }}
        .section h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .file-card {{ border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }}
        .file-stats {{ display: flex; gap: 20px; margin-bottom: 15px; }}
        .stat {{ background: #ecf0f1; padding: 5px 10px; border-radius: 3px; font-size: 0.9em; }}
        .status-success {{ background: #d5f4e6; color: #27ae60; }}
        .status-warning {{ background: #fef9e7; color: #f39c12; }}
        .status-danger {{ background: #fadbd8; color: #e74c3c; }}
        .issues-list {{ margin-top: 15px; }}
        .issue-item {{ padding: 10px; margin-bottom: 8px; border-left: 4px solid #ddd; background: #f9f9f9; }}
        .issue-item.severity-critical {{ border-left-color: #e74c3c; }}
        .issue-item.severity-high {{ border-left-color: #f39c12; }}
        .issue-item.severity-medium {{ border-left-color: #f1c40f; }}
        .issue-item.severity-low {{ border-left-color: #27ae60; }}
        .type-item {{ display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }}
        .type-count {{ font-weight: bold; color: #3498db; }}
        .recommendations {{ background: #e8f6f3; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60; }}
        .more-issues {{ text-align: center; color: #7f8c8d; font-style: italic; margin-top: 10px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 {title}</h1>
        
        <div class="header-info">
            <strong>📅 Date de génération:</strong> {timestamp}<br>
            <strong>📄 Fichiers analysés:</strong> {total_files}<br>
            <strong>🚨 Total problèmes:</strong> {total_issues}
        </div>
        
        <div class="stats-grid">
            <div class="stat-card critical">
                <span class="stat-number">{critical_count}</span>
                <span class="stat-label">🔴 Critique</span>
            </div>
            <div class="stat-card high">
                <span class="stat-number">{high_count}</span>
                <span class="stat-label">🟠 Élevé</span>
            </div>
            <div class="stat-card medium">
                <span class="stat-number">{medium_count}</span>
                <span class="stat-label">🟡 Moyen</span>
            </div>
            <div class="stat-card low">
                <span class="stat-number">{low_count}</span>
                <span class="stat-label">🔵 Faible</span>
            </div>
        </div>
        
        <div class="section">
            <h2>📄 Détails par Fichier</h2>
            {files_details}
        </div>
        
        <div class="section">
            <h2>📊 Problèmes par Type</h2>
            {issues_by_type}
        </div>
        
        <div class="section">
            <h2>💡 Recommandations</h2>
            <div class="recommendations">
                {recommendations}
            </div>
        </div>
    </div>
</body>
</html>"""
