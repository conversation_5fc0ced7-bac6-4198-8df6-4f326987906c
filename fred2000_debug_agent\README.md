# 🔧 Debug Agent - Projet Complet

Agent de débogage spécialisé pour les fichiers .ec du système FRED2000.

## 📁 Structure du Projet

```
fred2000_debug_agent/
├── 📂 core/                    # Moteur principal
│   ├── fred2000_debug_agent.py # Agent principal
│   ├── error_detectors.py      # Détecteurs d'erreurs
│   └── static_analyzer.py      # Analyseur statique
├── 📂 web/                     # Interface web
│   ├── app.py                  # Serveur Flask
│   ├── file_explorer_app.py    # Explorateur de fichiers
│   ├── templates/              # Templates HTML

│   └── static/                 # CSS/JS
├── 📂 cli/                     # Interface ligne de commande
│   └── debug_interface.py      # Interface CLI
├── 📂 reports/                 # Génération de rapports
│   └── report_generator.py     # Générateur de rapports
├── 📂 scripts/                 # Scripts de lancement
│   ├── LANCER_WEB_APP.bat     # Lancement Windows
│   └── run_web_app.py         # Script Python
├── 📂 tests/                   # Tests
│   └── test_agent.py          # Tests de fonctionnement
├── 📂 docs/                    # Documentation
│   ├── README_WEB.md          # Guide interface web
│   └── GUIDE_UTILISATION.md   # Guide complet
├── requirements.txt            # Dépendances Python
└── README.md                  # Ce fichier
```

## 🚀 Démarrage Rapide

### Interface Web (Recommandée)
```bash
cd fred2000_debug_agent/scripts
python run_web_app.py
```

### Interface Ligne de Commande
```bash
cd fred2000_debug_agent/cli
python debug_interface.py
```

## 📖 Documentation

- **[Guide Web](docs/README_WEB.md)** - Interface web moderne
- **[Guide Complet](docs/GUIDE_UTILISATION.md)** - Documentation détaillée

## 🎯 Fonctionnalités

- 🔍 **Analyse complète** des fichiers .ec
- 🌐 **Interface web** avec explorateur de fichiers
- 📊 **Rapports détaillés** en HTML/JSON/CSV
- 🚀 **Facile à utiliser** - sélection native de fichiers

---

**Version 1.0** - Agent de débogage professionnel pour FRED2000
