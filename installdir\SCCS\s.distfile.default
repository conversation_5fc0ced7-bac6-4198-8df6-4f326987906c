h39144
s 00006/00006/00052
d D 1.2 05/10/25 14:17:14 heims 2 1
c 
e
s 00058/00000/00000
d D 1.1 02/05/29 18:58:30 soueiha 1 0
c date and time created 02/05/29 18:58:30 by soueiha
e
u
U
t
T
I 1
#
# distfile for update remote hosts
#
#
# need remsh login (.rhosts) !
#
####
####  update from hvtest2 (HPUX.11) to default hosts
####
####  execute with 'make rinstall'
####
#
#
# the HOSTS and USER are created in script "tools/bin/rinstall" and 
# given with "-d USER=sername -d HOSTS=.. "
# HOSTS is eiter a given lit of hostnames or all hosts (configured in 
# fors_neu/environ/rinstall.cfg)
#  HOSTS = ( hostlist )
#  USER  = ( username )
#
# this both shell scripts are executed on remote host before and after update
# (also set from rinstall script with -d)
# TMP_EXEC_START=/tmp/exec_start-${USER}.sh
# TMP_EXEC_END=/tmp/exec_end-${USER}.sh

LEONI_BIN=/usr/forsgb/leoni/bin
LEONI_CTRL=/usr/forsgb/leoni/spool/ctrl
DETEC_PAGE=/home/<USER>/pagelib/prod

# filelist: (TMP_EXEC_START must be first file, TMP_EXEC_END the last)
D 2
FILES =( ${TMP_EXEC_START} 
         ${LEONI_BIN}/fvbco020 
E 2
I 2
FILES =( ${TMP_EXEC_START}
	 ${LEONI_BIN}/fvbco020
E 2
	 ${TMP_EXEC_END} )

# exclude filelist (complete path!)
EXCLUDE_FILES = ( )
EXCLUDE_NO_EXEC_FILE = ( /tmp/no_exec_file )

# exclude file pattern
EXCLUDE_TMP_EXTENSION = ( \\.tmp\$ \\.save\$ )
EXCLUDE_CFG_EXTENSION = ( \\.param\$ \\.bat\$ )

# dont copy files given in "except" or files that match pattern "except_pat"
${FILES} -> ${HOSTS}
D 2
        install ;
E 2
I 2
	install ;
E 2
	except ${EXCLUDE_FILES} ;
	except ${EXCLUDE_NO_EXEC_FILE} ;
D 2
        except_pat ${EXCLUDE_TMP_EXTENSION} ;
        except_pat ${EXCLUDE_CFG_EXTENSION} ;
E 2
I 2
	except_pat ${EXCLUDE_TMP_EXTENSION} ;
	except_pat ${EXCLUDE_CFG_EXTENSION} ;
E 2
	special ${TMP_EXEC_START} "sh ${TMP_EXEC_START} ; rm -f ${TMP_EXEC_START}" ;
	special ${TMP_EXEC_END}   "sh ${TMP_EXEC_END} ; rm -f ${TMP_EXEC_END}" ;

D 2
        #notify <EMAIL> , name2 ;
E 2
I 2
	#notify <EMAIL> , name2 ;
E 2

# for notify: sendmail must be configured on machine, or can be loginname 
# (without @domain.xx) on local machine. Mail on local machine (var/mail/) 
# can be read with 'mail' command

E 1
