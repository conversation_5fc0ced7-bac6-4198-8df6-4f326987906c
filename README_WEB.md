# 🌐 Debug Agent - Application Web

Interface web moderne pour l'analyse des fichiers `.ec` du système FRED2000.

## 🚀 Démarrage Rapide

### Option 1: Lancement Automatique (Recommandé)
1. **Double-cliquez** sur `LANCER_WEB_APP.bat`
2. L'application s'ouvre automatiquement dans votre navigateur
3. Commencez à analyser vos fichiers !

### Option 2: Lancement Manuel
```bash
# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application
python run_web_app.py
```

### Option 3: Développement
```bash
python app.py
```

## 📱 Interface Web

L'application web est accessible sur **http://localhost:5000**

### 🎯 Fonctionnalités Principales

#### 1. 📤 Upload de Fichiers
- **Glisser-déposer** des fichiers `.ec` ou archives ZIP
- **Sélection multiple** de fichiers
- **Extraction automatique** des archives ZIP
- **Prévisualisation** des fichiers uploadés

#### 2. ⚙️ Configuration d'Analyse
- **5 types d'analyse** configurables :
  - 🗄️ **SQL/ESQL** - Erreurs dans les requêtes SQL
  - 🧠 **Mémoire** - Buffer overflow, fuites mémoire
  - 📊 **Logique Métier** - Spécifique à FRED2000
  - ⚡ **Performance** - Optimisations possibles
  - 🔬 **Analyse Statique** - Analyse approfondie

#### 3. 📊 Suivi en Temps Réel
- **Barre de progression** en temps réel
- **Logs détaillés** de l'analyse
- **WebSockets** pour les mises à jour instantanées

#### 4. 📈 Résultats Interactifs
- **Statistiques visuelles** par gravité
- **Détails par fichier** avec code source
- **Suggestions de correction** pour chaque problème
- **Classification** par type et gravité

#### 5. 📄 Export de Rapports
- **HTML** - Rapport complet avec graphiques
- **JSON** - Données structurées
- **CSV** - Pour Excel/LibreOffice

## 🎨 Interface Utilisateur

### Design Moderne
- **Interface responsive** - Fonctionne sur mobile/tablette
- **Animations fluides** - Transitions CSS3
- **Thème moderne** - Dégradés et ombres
- **Icons Font Awesome** - Interface professionnelle

### Navigation Intuitive
- **4 onglets principaux** :
  1. 📤 **Upload** - Téléchargement des fichiers
  2. 🔍 **Analyse** - Configuration et lancement
  3. 📊 **Résultats** - Visualisation des problèmes
  4. 📄 **Rapports** - Export des données

## 🔧 Architecture Technique

### Backend (Flask)
- **API REST** pour toutes les opérations
- **WebSockets** pour le temps réel
- **Upload sécurisé** avec validation
- **Analyse asynchrone** en threads
- **Génération de rapports** multi-formats

### Frontend (Vanilla JS)
- **Interface moderne** HTML5/CSS3/JS
- **WebSocket client** pour temps réel
- **Upload drag & drop** intuitif
- **Notifications toast** pour feedback
- **Responsive design** mobile-friendly

### Sécurité
- **Validation des fichiers** uploadés
- **Noms de fichiers sécurisés**
- **Sessions isolées** par utilisateur
- **CORS configuré** pour développement

## 📊 Types d'Erreurs Détectées

### 🗄️ Erreurs SQL/ESQL
```c
EXEC SQL SELECT * FROM table;  // ❌ Sans gestion d'erreur
// ✅ Solution: Ajouter if (sqlca.sqlcode != 0) { ... }
```

### 🧠 Problèmes Mémoire
```c
strcpy(buffer, source);        // ❌ Buffer overflow
// ✅ Solution: strncpy(buffer, source, sizeof(buffer)-1);
```

### 📊 Logique Métier FRED2000
```c
return ERROR;                  // ❌ Sans logging
// ✅ Solution: log_ent("Erreur"); return ERROR;
```

### ⚡ Performance
```c
for (i = 0; i < count; i++) {
    EXEC SQL SELECT ...;       // ❌ SQL dans boucle
}
// ✅ Solution: Une seule requête avec JOIN
```

## 🎯 Utilisation Étape par Étape

### 1. Démarrage
```bash
# Lancer l'application
python run_web_app.py

# Ou double-clic sur LANCER_WEB_APP.bat
```

### 2. Upload des Fichiers
1. Ouvrez http://localhost:5000
2. Glissez vos fichiers `.ec` dans la zone d'upload
3. Ou cliquez "Sélectionner des fichiers"
4. Vérifiez la liste des fichiers détectés

### 3. Configuration
1. Allez dans l'onglet "Analyse"
2. Sélectionnez les types d'analyse souhaités
3. Cliquez "Démarrer l'analyse"

### 4. Suivi
1. Suivez la progression en temps réel
2. Consultez les logs détaillés
3. Attendez la fin de l'analyse

### 5. Résultats
1. L'onglet "Résultats" s'ouvre automatiquement
2. Consultez les statistiques par gravité
3. Examinez les détails par fichier
4. Lisez les suggestions de correction

### 6. Export
1. Allez dans l'onglet "Rapports"
2. Choisissez le format (HTML/JSON/CSV)
3. Téléchargez le rapport

## 🔍 API Endpoints

### Upload
```
POST /api/upload
- Upload de fichiers .ec ou ZIP
- Retourne session_id et liste des fichiers
```

### Analyse
```
POST /api/analyze
- Lance l'analyse avec options
- Retourne analysis_id
```

### Statut
```
GET /api/analysis/{analysis_id}
- Récupère le statut d'une analyse
```

### Rapports
```
GET /api/analysis/{analysis_id}/report/{format}
- Télécharge un rapport (html/json/csv)
```

### WebSocket Events
```
analysis_progress - Progression en temps réel
analysis_complete - Analyse terminée
```

## 🛠️ Développement

### Structure des Fichiers
```
fred2000_web/
├── app.py                    # Backend Flask
├── run_web_app.py           # Script de lancement
├── requirements.txt         # Dépendances Python
├── templates/
│   └── index.html          # Interface web
├── static/
│   └── app.js              # JavaScript frontend
├── uploads/                # Fichiers uploadés
├── reports/                # Rapports générés
└── temp/                   # Fichiers temporaires
```

### Ajout de Nouvelles Fonctionnalités

#### Nouveau Type d'Analyse
1. Créez un détecteur dans `error_detectors.py`
2. Ajoutez-le dans `analyze_single_file()` dans `app.py`
3. Ajoutez l'option dans l'interface HTML

#### Nouveau Format de Rapport
1. Étendez `FRED2000ReportGenerator` dans `report_generator.py`
2. Ajoutez l'endpoint dans `app.py`
3. Ajoutez le bouton dans l'interface

## 🚨 Dépannage

### Problèmes Courants

**"Module not found"**
```bash
pip install -r requirements.txt
```

**"Port 5000 already in use"**
- Changez le port dans `app.py` : `port=5001`
- Ou arrêtez l'autre application sur le port 5000

**"Upload failed"**
- Vérifiez que les fichiers sont bien des `.ec`
- Vérifiez la taille (max 100MB)
- Consultez les logs du serveur

**"Analysis stuck"**
- Rechargez la page
- Vérifiez les logs du serveur
- Redémarrez l'application

### Logs de Débogage
- Les logs du serveur apparaissent dans la console
- Les erreurs JavaScript dans la console du navigateur (F12)
- Les fichiers temporaires dans le dossier `uploads/`

## 🎉 Avantages de l'Interface Web

✅ **Aucune installation** - Fonctionne dans le navigateur  
✅ **Multi-plateforme** - Windows, Linux, macOS  
✅ **Interface moderne** - Design professionnel  
✅ **Temps réel** - Suivi instantané de l'analyse  
✅ **Multi-utilisateurs** - Sessions isolées  
✅ **Responsive** - Fonctionne sur mobile  
✅ **Extensible** - Facile d'ajouter des fonctionnalités  

---

**🌐 Votre interface web FRED2000 est prête !** Lancez `LANCER_WEB_APP.bat` et commencez à analyser vos fichiers dans une interface moderne et intuitive.
