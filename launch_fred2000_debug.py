#!/usr/bin/env python3
"""
Script de lancement pour l'Agent de Débogage FRED2000
Lance l'interface graphique avec gestion d'erreurs

Auteur: Assistant IA
Date: 2025-01-14
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

def check_dependencies():
    """Vérifie que toutes les dépendances sont présentes"""
    missing_modules = []
    
    # Modules Python standard requis
    required_modules = [
        'tkinter', 'threading', 'json', 'glob', 'pathlib', 'datetime'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    # Modules personnalisés
    custom_modules = [
        'fred2000_debug_agent',
        'error_detectors', 
        'static_analyzer',
        'fred2000_gui'
    ]
    
    for module in custom_modules:
        if not os.path.exists(f"{module}.py"):
            missing_modules.append(f"{module}.py")
    
    return missing_modules

def show_welcome_message():
    """Affiche un message de bienvenue"""
    print("=" * 60)
    print("🔧 DEBUG AGENT - Interface Graphique")
    print("   Agent de Débogage Spécialisé pour FRED2000")
    print("   Version 1.0 - 2025-01-14")
    print("=" * 60)
    print()
    print("🚀 Lancement de l'interface graphique...")
    print()

def main():
    """Fonction principale de lancement"""
    try:
        show_welcome_message()
        
        # Vérifier les dépendances
        print("🔍 Vérification des dépendances...")
        missing = check_dependencies()
        
        if missing:
            error_msg = "❌ Modules manquants:\n" + "\n".join(f"  - {m}" for m in missing)
            print(error_msg)
            
            # Essayer d'afficher une boîte de dialogue si tkinter fonctionne
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Dépendances manquantes", error_msg)
                root.destroy()
            except:
                pass
            
            return 1
        
        print("✅ Toutes les dépendances sont présentes")
        
        # Importer et lancer l'interface
        print("🎨 Chargement de l'interface graphique...")
        
        try:
            from fred2000_gui import FRED2000GUI
            
            print("✅ Interface chargée avec succès")
            print("📱 Ouverture de la fenêtre principale...")
            
            # Créer et lancer l'application
            app = FRED2000GUI()
            app.run()
            
            print("👋 Interface fermée")
            return 0
            
        except ImportError as e:
            error_msg = f"❌ Erreur d'import: {e}\n\nAssurez-vous que tous les fichiers sont dans le même dossier."
            print(error_msg)
            
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Erreur d'import", error_msg)
                root.destroy()
            except:
                pass
            
            return 1
    
    except KeyboardInterrupt:
        print("\n👋 Arrêt demandé par l'utilisateur")
        return 0
    
    except Exception as e:
        error_msg = f"❌ Erreur inattendue: {e}\n\nDétails:\n{traceback.format_exc()}"
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erreur", f"Erreur inattendue: {e}")
            root.destroy()
        except:
            pass
        
        return 1

if __name__ == "__main__":
    # Changer le répertoire de travail vers le dossier du script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Lancer l'application
    exit_code = main()
    
    # Pause avant fermeture si lancé depuis l'explorateur
    if len(sys.argv) == 1:  # Pas d'arguments = double-clic
        input("\nAppuyez sur Entrée pour fermer...")
    
    sys.exit(exit_code)
