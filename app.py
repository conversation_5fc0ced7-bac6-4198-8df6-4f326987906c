#!/usr/bin/env python3
"""
Debug Agent - Backend API Flask
Interface web pour l'analyse des fichiers .ec

Auteur: Assistant IA
Date: 2025-01-14
"""

from flask import Flask, request, jsonify, render_template, send_file
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room
import os
import json
import uuid
import threading
import time
from datetime import datetime
from werkzeug.utils import secure_filename
import zipfile
import tempfile
import shutil

# Import des modules d'analyse
try:
    from fred2000_debug_agent import FRED2000DebugAgent
    from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
    from static_analyzer import FRED2000StaticAnalyzer
    from report_generator import FRED2000ReportGenerator
except ImportError as e:
    print(f"Erreur d'import: {e}")

# Configuration Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'fred2000-debug-agent-secret-key'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max

# Configuration CORS et SocketIO
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Variables globales
active_analyses = {}
upload_sessions = {}

# Créer les dossiers nécessaires
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('reports', exist_ok=True)
os.makedirs('temp', exist_ok=True)

def allowed_file(filename):
    """Vérifie si le fichier est autorisé"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['ec', 'zip']

def emit_progress(session_id, message, progress=None):
    """Émet un message de progression via WebSocket"""
    data = {
        'message': message,
        'timestamp': datetime.now().isoformat()
    }
    if progress is not None:
        data['progress'] = progress
    
    socketio.emit('analysis_progress', data, room=session_id)

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """Vérification de l'état de l'API"""
    return jsonify({
        'status': 'healthy',
        'version': '1.0',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/upload', methods=['POST'])
def upload_files():
    """Upload de fichiers .ec ou archives ZIP"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400
        
        files = request.files.getlist('files')
        session_id = str(uuid.uuid4())
        
        uploaded_files = []
        upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
        os.makedirs(upload_dir, exist_ok=True)
        
        for file in files:
            if file.filename == '':
                continue
                
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(upload_dir, filename)
                file.save(file_path)
                
                # Si c'est un ZIP, l'extraire
                if filename.lower().endswith('.zip'):
                    extract_dir = os.path.join(upload_dir, 'extracted')
                    os.makedirs(extract_dir, exist_ok=True)
                    
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                    
                    # Chercher les fichiers .ec dans l'archive
                    for root, dirs, files in os.walk(extract_dir):
                        for f in files:
                            if f.lower().endswith('.ec'):
                                ec_path = os.path.join(root, f)
                                rel_path = os.path.relpath(ec_path, extract_dir)
                                uploaded_files.append({
                                    'name': rel_path,
                                    'path': ec_path,
                                    'size': os.path.getsize(ec_path),
                                    'type': 'ec'
                                })
                else:
                    uploaded_files.append({
                        'name': filename,
                        'path': file_path,
                        'size': os.path.getsize(file_path),
                        'type': 'ec'
                    })
        
        upload_sessions[session_id] = {
            'files': uploaded_files,
            'upload_time': datetime.now().isoformat(),
            'status': 'uploaded'
        }
        
        return jsonify({
            'session_id': session_id,
            'files': uploaded_files,
            'message': f'{len(uploaded_files)} fichier(s) .ec uploadé(s) avec succès'
        })
        
    except Exception as e:
        return jsonify({'error': f'Erreur lors de l\'upload: {str(e)}'}), 500

@app.route('/api/sessions/<session_id>/files')
def get_session_files(session_id):
    """Récupère la liste des fichiers d'une session"""
    if session_id not in upload_sessions:
        return jsonify({'error': 'Session non trouvée'}), 404
    
    return jsonify(upload_sessions[session_id])

@app.route('/api/analyze', methods=['POST'])
def start_analysis():
    """Lance l'analyse des fichiers"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        selected_files = data.get('files', [])
        analysis_options = data.get('options', {})
        
        if session_id not in upload_sessions:
            return jsonify({'error': 'Session non trouvée'}), 404
        
        # Créer un ID d'analyse unique
        analysis_id = str(uuid.uuid4())
        
        # Préparer les données d'analyse
        analysis_data = {
            'id': analysis_id,
            'session_id': session_id,
            'files': selected_files,
            'options': analysis_options,
            'status': 'starting',
            'start_time': datetime.now().isoformat(),
            'progress': 0,
            'results': {}
        }
        
        active_analyses[analysis_id] = analysis_data
        
        # Lancer l'analyse dans un thread séparé
        thread = threading.Thread(
            target=run_analysis_thread,
            args=(analysis_id, session_id, selected_files, analysis_options)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'analysis_id': analysis_id,
            'message': 'Analyse démarrée',
            'status': 'starting'
        })
        
    except Exception as e:
        return jsonify({'error': f'Erreur lors du démarrage de l\'analyse: {str(e)}'}), 500

def run_analysis_thread(analysis_id, session_id, selected_files, options):
    """Exécute l'analyse dans un thread séparé"""
    try:
        analysis = active_analyses[analysis_id]
        session = upload_sessions[session_id]
        
        analysis['status'] = 'running'
        emit_progress(session_id, "🚀 Démarrage de l'analyse...", 0)
        
        total_files = len(selected_files)
        results = {}
        
        for i, file_name in enumerate(selected_files):
            # Trouver le fichier dans la session
            file_info = None
            for f in session['files']:
                if f['name'] == file_name:
                    file_info = f
                    break
            
            if not file_info:
                continue
            
            progress = (i / total_files) * 100
            emit_progress(session_id, f"📄 Analyse de {file_name}...", progress)
            
            # Analyser le fichier
            file_results = analyze_single_file(file_info['path'], options)
            results[file_name] = file_results
            
            emit_progress(session_id, f"✅ {file_name} analysé: {len(file_results.get('issues', []))} problèmes")
        
        # Finaliser l'analyse
        analysis['status'] = 'completed'
        analysis['progress'] = 100
        analysis['results'] = results
        analysis['end_time'] = datetime.now().isoformat()
        
        # Calculer les statistiques
        total_issues = sum(len(result.get('issues', [])) for result in results.values())
        
        emit_progress(session_id, f"🎉 Analyse terminée! {total_issues} problèmes détectés au total", 100)
        
        # Émettre les résultats finaux
        socketio.emit('analysis_complete', {
            'analysis_id': analysis_id,
            'results': results,
            'statistics': calculate_statistics(results)
        }, room=session_id)
        
    except Exception as e:
        analysis['status'] = 'error'
        analysis['error'] = str(e)
        emit_progress(session_id, f"❌ Erreur lors de l'analyse: {str(e)}")

def analyze_single_file(file_path, options):
    """Analyse un seul fichier"""
    results = {
        'file_path': file_path,
        'issues': [],
        'stats': {},
        'analysis_types': []
    }
    
    try:
        # Créer l'agent pour ce fichier
        agent = FRED2000DebugAgent(file_path)
        
        # Statistiques de base
        results['stats'] = {
            'total_lines': len(agent.code_lines),
            'total_functions': len(agent.functions),
            'file_size': os.path.getsize(file_path)
        }
        
        all_issues = []
        
        # Analyses selon les options
        if options.get('sql_analysis', True):
            sql_detector = SQLErrorDetector()
            sql_issues = sql_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
            all_issues.extend(sql_issues)
            results['analysis_types'].append('SQL')
        
        if options.get('memory_analysis', True):
            memory_detector = MemoryErrorDetector()
            memory_issues = memory_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
            all_issues.extend(memory_issues)
            results['analysis_types'].append('Mémoire')
        
        if options.get('business_analysis', True):
            business_detector = BusinessLogicDetector()
            business_issues = business_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
            all_issues.extend(business_issues)
            results['analysis_types'].append('Métier')
        
        if options.get('performance_analysis', True):
            performance_detector = PerformanceDetector()
            performance_issues = performance_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
            all_issues.extend(performance_issues)
            results['analysis_types'].append('Performance')
        
        if options.get('static_analysis', False):
            static_analyzer = FRED2000StaticAnalyzer()
            static_issues = static_analyzer.analyze_code(agent.code_lines)
            all_issues.extend(static_issues)
            results['analysis_types'].append('Statique')
        
        # Convertir les issues en dictionnaires
        results['issues'] = [
            {
                'type': issue.error_type.value,
                'severity': issue.severity.value,
                'line': issue.line_number,
                'function': issue.function_name,
                'description': issue.description,
                'code': issue.code_snippet,
                'suggestion': issue.suggestion
            }
            for issue in all_issues
        ]
        
    except Exception as e:
        results['error'] = str(e)
    
    return results

def calculate_statistics(results):
    """Calcule les statistiques globales"""
    stats = {
        'total_files': len(results),
        'total_issues': 0,
        'severity_counts': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
        'type_counts': {}
    }
    
    for result in results.values():
        issues = result.get('issues', [])
        stats['total_issues'] += len(issues)
        
        for issue in issues:
            severity = issue.get('severity', 'unknown')
            issue_type = issue.get('type', 'unknown')
            
            if severity in stats['severity_counts']:
                stats['severity_counts'][severity] += 1
            
            stats['type_counts'][issue_type] = stats['type_counts'].get(issue_type, 0) + 1
    
    return stats

@app.route('/api/analysis/<analysis_id>')
def get_analysis_status(analysis_id):
    """Récupère le statut d'une analyse"""
    if analysis_id not in active_analyses:
        return jsonify({'error': 'Analyse non trouvée'}), 404
    
    return jsonify(active_analyses[analysis_id])

@app.route('/api/analysis/<analysis_id>/report/<format>')
def generate_report(analysis_id, format):
    """Génère un rapport dans le format demandé"""
    if analysis_id not in active_analyses:
        return jsonify({'error': 'Analyse non trouvée'}), 404
    
    analysis = active_analyses[analysis_id]
    if analysis['status'] != 'completed':
        return jsonify({'error': 'Analyse non terminée'}), 400
    
    try:
        generator = FRED2000ReportGenerator()
        results = analysis['results']
        
        if format == 'html':
            report_file = generator.generate_html_report(results, f'reports/report_{analysis_id}.html')
            return send_file(report_file, as_attachment=True)
        elif format == 'json':
            report_file = generator.generate_json_report(results, f'reports/report_{analysis_id}.json')
            return send_file(report_file, as_attachment=True)
        elif format == 'csv':
            report_file = generator.generate_csv_report(results, f'reports/report_{analysis_id}.csv')
            return send_file(report_file, as_attachment=True)
        else:
            return jsonify({'error': 'Format non supporté'}), 400
            
    except Exception as e:
        return jsonify({'error': f'Erreur lors de la génération du rapport: {str(e)}'}), 500

@socketio.on('connect')
def handle_connect():
    """Gestion de la connexion WebSocket"""
    print(f'Client connecté: {request.sid}')

@socketio.on('disconnect')
def handle_disconnect():
    """Gestion de la déconnexion WebSocket"""
    print(f'Client déconnecté: {request.sid}')

@socketio.on('join_session')
def handle_join_session(data):
    """Rejoindre une session pour recevoir les mises à jour"""
    session_id = data.get('session_id')
    if session_id:
        join_room(session_id)
        emit('joined_session', {'session_id': session_id})

if __name__ == '__main__':
    print("🚀 Démarrage du serveur Debug Agent")
    print("📱 Interface web disponible sur: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
