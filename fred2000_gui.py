#!/usr/bin/env python3
"""
Interface Graphique Moderne pour l'Agent de Débogage FRED2000
Interface tkinter avec sélection de dossier et analyse automatique

Auteur: Assistant IA
Date: 2025-01-14
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import glob
import threading
from pathlib import Path
import json
from datetime import datetime
from typing import List, Dict, Optional

# Import des modules de l'agent
try:
    from fred2000_debug_agent import FRED2000DebugAgent, ErrorType, Severity
    from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
    from static_analyzer import FRED2000StaticAnalyzer
except ImportError as e:
    print(f"Erreur d'import: {e}")
    print("Assurez-vous que tous les modules sont dans le même dossier")

class FRED2000GUI:
    """Interface graphique principale pour l'agent de débogage FRED2000"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()

        # Variables de travail
        self.current_folder = ""
        self.ec_files = []
        self.selected_files = []
        self.analysis_results = {}
        self.is_analyzing = False

    def setup_window(self):
        """Configure la fenêtre principale"""
        self.root.title("🔧 Debug Agent - Interface Graphique")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Style moderne
        style = ttk.Style()
        style.theme_use('clam')

        # Couleurs personnalisées
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Success.TLabel', foreground='#27ae60')
        style.configure('Error.TLabel', foreground='#e74c3c')
        style.configure('Warning.TLabel', foreground='#f39c12')

    def setup_variables(self):
        """Initialise les variables tkinter"""
        self.folder_var = tk.StringVar()
        self.status_var = tk.StringVar(value="Prêt - Sélectionnez un dossier")
        self.progress_var = tk.DoubleVar()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal avec padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Titre
        title_label = ttk.Label(main_frame, text="🔧 Debug Agent", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Section sélection de dossier
        self.create_folder_selection_section(main_frame, row=1)

        # Section principale avec onglets
        self.create_main_tabs_section(main_frame, row=2)

        # Barre de statut
        self.create_status_bar(main_frame, row=3)

    def create_folder_selection_section(self, parent, row):
        """Crée la section de sélection de dossier"""
        folder_frame = ttk.LabelFrame(parent, text="📁 Sélection du Dossier", padding="10")
        folder_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        folder_frame.columnconfigure(1, weight=1)

        # Bouton parcourir
        browse_btn = ttk.Button(folder_frame, text="📂 Parcourir...", command=self.browse_folder)
        browse_btn.grid(row=0, column=0, padx=(0, 10))

        # Champ dossier
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, state='readonly')
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # Bouton scanner
        scan_btn = ttk.Button(folder_frame, text="🔍 Scanner", command=self.scan_folder)
        scan_btn.grid(row=0, column=2)

    def create_main_tabs_section(self, parent, row):
        """Crée la section principale avec onglets"""
        # Notebook pour les onglets
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # Onglet 1: Fichiers
        self.create_files_tab()

        # Onglet 2: Analyse
        self.create_analysis_tab()

        # Onglet 3: Résultats
        self.create_results_tab()

        # Onglet 4: Rapports
        self.create_reports_tab()

    def create_files_tab(self):
        """Crée l'onglet de gestion des fichiers"""
        files_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(files_frame, text="📄 Fichiers .ec")

        files_frame.columnconfigure(0, weight=1)
        files_frame.rowconfigure(1, weight=1)

        # En-tête
        header_label = ttk.Label(files_frame, text="Fichiers .ec détectés:", style='Header.TLabel')
        header_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Liste des fichiers avec checkboxes
        list_frame = ttk.Frame(files_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Treeview pour les fichiers
        columns = ('Sélection', 'Fichier', 'Taille', 'Modifié')
        self.files_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        self.files_tree.heading('Sélection', text='✓')
        self.files_tree.heading('Fichier', text='Nom du fichier')
        self.files_tree.heading('Taille', text='Taille')
        self.files_tree.heading('Modifié', text='Dernière modification')

        self.files_tree.column('Sélection', width=50, anchor=tk.CENTER)
        self.files_tree.column('Fichier', width=300)
        self.files_tree.column('Taille', width=100, anchor=tk.E)
        self.files_tree.column('Modifié', width=150, anchor=tk.CENTER)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.files_tree.xview)
        self.files_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid des éléments
        self.files_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Bind double-click pour toggle sélection
        self.files_tree.bind('<Double-1>', self.toggle_file_selection)

        # Boutons de contrôle
        control_frame = ttk.Frame(files_frame)
        control_frame.grid(row=2, column=0, pady=(10, 0), sticky=tk.W)

        ttk.Button(control_frame, text="✓ Tout sélectionner", command=self.select_all_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="✗ Tout désélectionner", command=self.deselect_all_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="🔍 Analyser sélectionnés", command=self.start_analysis).pack(side=tk.LEFT, padx=(10, 0))

    def create_analysis_tab(self):
        """Crée l'onglet d'analyse"""
        analysis_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(analysis_frame, text="🔍 Analyse")

        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(1, weight=1)

        # Options d'analyse
        options_frame = ttk.LabelFrame(analysis_frame, text="Options d'analyse", padding="10")
        options_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Variables pour les options
        self.sql_analysis_var = tk.BooleanVar(value=True)
        self.memory_analysis_var = tk.BooleanVar(value=True)
        self.business_analysis_var = tk.BooleanVar(value=True)
        self.performance_analysis_var = tk.BooleanVar(value=True)
        self.static_analysis_var = tk.BooleanVar(value=False)

        # Checkboxes pour les types d'analyse
        ttk.Checkbutton(options_frame, text="🗄️ Analyse SQL/ESQL", variable=self.sql_analysis_var).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="🧠 Analyse Mémoire", variable=self.memory_analysis_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        ttk.Checkbutton(options_frame, text="📊 Logique Métier", variable=self.business_analysis_var).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="⚡ Performance", variable=self.performance_analysis_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        ttk.Checkbutton(options_frame, text="🔬 Analyse Statique", variable=self.static_analysis_var).grid(row=2, column=0, sticky=tk.W)

        # Zone de progression et logs
        progress_frame = ttk.LabelFrame(analysis_frame, text="Progression", padding="10")
        progress_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        progress_frame.columnconfigure(0, weight=1)
        progress_frame.rowconfigure(1, weight=1)

        # Barre de progression
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Zone de logs
        self.log_text = scrolledtext.ScrolledText(progress_frame, height=20, wrap=tk.WORD)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def create_results_tab(self):
        """Crée l'onglet des résultats"""
        results_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(results_frame, text="📊 Résultats")

        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Placeholder pour les résultats
        self.results_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.results_text.insert(tk.END, "Les résultats d'analyse apparaîtront ici...\n\n")
        self.results_text.insert(tk.END, "🔍 Sélectionnez des fichiers .ec et lancez l'analyse pour voir les résultats.")

    def create_reports_tab(self):
        """Crée l'onglet des rapports"""
        reports_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(reports_frame, text="📄 Rapports")

        reports_frame.columnconfigure(0, weight=1)

        # En-tête
        ttk.Label(reports_frame, text="Génération de rapports", style='Header.TLabel').pack(pady=(0, 20))

        # Section formats d'export
        export_frame = ttk.LabelFrame(reports_frame, text="Formats d'export", padding="15")
        export_frame.pack(fill=tk.X, pady=(0, 20))

        # Boutons d'export
        buttons_frame = ttk.Frame(export_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="📄 Rapport HTML",
                  command=self.export_html_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="📊 Données JSON",
                  command=self.export_json_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="📋 Liste CSV",
                  command=self.export_csv_report).pack(side=tk.LEFT, padx=(0, 10))

        # Description des formats
        desc_frame = ttk.Frame(export_frame)
        desc_frame.pack(fill=tk.X, pady=(15, 0))

        descriptions = [
            "📄 HTML: Rapport complet avec graphiques et mise en forme",
            "📊 JSON: Données structurées pour intégration avec d'autres outils",
            "📋 CSV: Liste des problèmes pour analyse dans Excel/LibreOffice"
        ]

        for desc in descriptions:
            ttk.Label(desc_frame, text=desc, foreground='gray').pack(anchor=tk.W, pady=2)

        # Section historique des rapports
        history_frame = ttk.LabelFrame(reports_frame, text="Rapports générés", padding="15")
        history_frame.pack(fill=tk.BOTH, expand=True)

        # Liste des rapports
        self.reports_tree = ttk.Treeview(history_frame, columns=('Type', 'Date', 'Taille'), show='headings', height=8)

        self.reports_tree.heading('Type', text='Type')
        self.reports_tree.heading('Date', text='Date de création')
        self.reports_tree.heading('Taille', text='Taille')

        self.reports_tree.column('Type', width=100)
        self.reports_tree.column('Date', width=150)
        self.reports_tree.column('Taille', width=100)

        # Scrollbar pour la liste
        reports_scroll = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.reports_tree.yview)
        self.reports_tree.configure(yscrollcommand=reports_scroll.set)

        self.reports_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reports_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Boutons pour les rapports
        reports_buttons = ttk.Frame(history_frame)
        reports_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(reports_buttons, text="🔍 Ouvrir", command=self.open_selected_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(reports_buttons, text="📁 Dossier", command=self.open_reports_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(reports_buttons, text="🔄 Actualiser", command=self.refresh_reports_list).pack(side=tk.LEFT, padx=(0, 5))

        # Charger la liste des rapports existants
        self.refresh_reports_list()

    def create_status_bar(self, parent, row):
        """Crée la barre de statut"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)

        # Séparateur
        ttk.Separator(status_frame, orient=tk.HORIZONTAL).grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 5))

        # Label de statut
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, sticky=tk.W)

        # Version
        version_label = ttk.Label(status_frame, text="v1.0", foreground='gray')
        version_label.grid(row=1, column=1, sticky=tk.E)

    def browse_folder(self):
        """Ouvre le dialogue de sélection de dossier"""
        folder = filedialog.askdirectory(
            title="Sélectionnez le dossier contenant les fichiers .ec",
            initialdir=os.getcwd()
        )

        if folder:
            self.current_folder = folder
            self.folder_var.set(folder)
            self.status_var.set(f"Dossier sélectionné: {os.path.basename(folder)}")
            self.log_message(f"📁 Dossier sélectionné: {folder}")

            # Scanner automatiquement
            self.scan_folder()

    def scan_folder(self):
        """Scanne le dossier pour trouver les fichiers .ec"""
        if not self.current_folder:
            messagebox.showwarning("Attention", "Veuillez d'abord sélectionner un dossier")
            return

        try:
            # Chercher tous les fichiers .ec
            pattern = os.path.join(self.current_folder, "*.ec")
            ec_files = glob.glob(pattern)

            # Chercher aussi dans les sous-dossiers
            pattern_recursive = os.path.join(self.current_folder, "**", "*.ec")
            ec_files.extend(glob.glob(pattern_recursive, recursive=True))

            # Supprimer les doublons
            self.ec_files = list(set(ec_files))

            self.log_message(f"🔍 Scan terminé: {len(self.ec_files)} fichiers .ec trouvés")
            self.status_var.set(f"{len(self.ec_files)} fichiers .ec trouvés")

            # Mettre à jour la liste
            self.update_files_list()

            if self.ec_files:
                # Passer à l'onglet fichiers
                self.notebook.select(0)
            else:
                messagebox.showinfo("Information", "Aucun fichier .ec trouvé dans ce dossier")

        except Exception as e:
            self.log_message(f"❌ Erreur lors du scan: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du scan: {e}")

    def update_files_list(self):
        """Met à jour la liste des fichiers dans le treeview"""
        # Vider la liste
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)

        # Ajouter les fichiers
        for file_path in self.ec_files:
            try:
                # Informations sur le fichier
                stat = os.stat(file_path)
                size = self.format_file_size(stat.st_size)
                modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

                # Nom relatif
                rel_path = os.path.relpath(file_path, self.current_folder)

                # Ajouter à la liste
                item_id = self.files_tree.insert('', tk.END, values=('☐', rel_path, size, modified))

                # Stocker le chemin complet
                self.files_tree.set(item_id, 'full_path', file_path)

            except Exception as e:
                self.log_message(f"⚠ Erreur avec le fichier {file_path}: {e}")

    def format_file_size(self, size_bytes):
        """Formate la taille du fichier"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def toggle_file_selection(self, event):
        """Toggle la sélection d'un fichier"""
        item = self.files_tree.selection()[0]
        current_selection = self.files_tree.item(item, 'values')[0]

        if current_selection == '☐':
            # Sélectionner
            values = list(self.files_tree.item(item, 'values'))
            values[0] = '☑'
            self.files_tree.item(item, values=values)
        else:
            # Désélectionner
            values = list(self.files_tree.item(item, 'values'))
            values[0] = '☐'
            self.files_tree.item(item, values=values)

        self.update_selected_files()

    def select_all_files(self):
        """Sélectionne tous les fichiers"""
        for item in self.files_tree.get_children():
            values = list(self.files_tree.item(item, 'values'))
            values[0] = '☑'
            self.files_tree.item(item, values=values)

        self.update_selected_files()

    def deselect_all_files(self):
        """Désélectionne tous les fichiers"""
        for item in self.files_tree.get_children():
            values = list(self.files_tree.item(item, 'values'))
            values[0] = '☐'
            self.files_tree.item(item, values=values)

        self.update_selected_files()

    def update_selected_files(self):
        """Met à jour la liste des fichiers sélectionnés"""
        self.selected_files = []

        for item in self.files_tree.get_children():
            if self.files_tree.item(item, 'values')[0] == '☑':
                # Récupérer le chemin complet
                file_path = self.files_tree.set(item, 'full_path')
                if not file_path:  # Fallback si pas stocké
                    rel_path = self.files_tree.item(item, 'values')[1]
                    file_path = os.path.join(self.current_folder, rel_path)

                self.selected_files.append(file_path)

        count = len(self.selected_files)
        self.status_var.set(f"{count} fichier(s) sélectionné(s)")

    def log_message(self, message):
        """Ajoute un message au log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def start_analysis(self):
        """Lance l'analyse des fichiers sélectionnés"""
        if not self.selected_files:
            messagebox.showwarning("Attention", "Veuillez sélectionner au moins un fichier à analyser")
            return

        if self.is_analyzing:
            messagebox.showinfo("Information", "Une analyse est déjà en cours")
            return

        # Passer à l'onglet analyse
        self.notebook.select(1)

        # Lancer l'analyse dans un thread séparé
        self.is_analyzing = True
        analysis_thread = threading.Thread(target=self.run_analysis_thread)
        analysis_thread.daemon = True
        analysis_thread.start()

    def run_analysis_thread(self):
        """Exécute l'analyse dans un thread séparé"""
        try:
            self.log_message("🚀 Démarrage de l'analyse...")
            self.progress_var.set(0)

            total_files = len(self.selected_files)
            self.analysis_results = {}

            for i, file_path in enumerate(self.selected_files):
                # Mise à jour du progrès
                progress = (i / total_files) * 100
                self.progress_var.set(progress)

                file_name = os.path.basename(file_path)
                self.log_message(f"📄 Analyse de {file_name}...")

                # Analyser le fichier
                file_results = self.analyze_single_file(file_path)
                self.analysis_results[file_path] = file_results

                self.log_message(f"✅ {file_name} analysé: {len(file_results.get('issues', []))} problèmes détectés")

            # Analyse terminée
            self.progress_var.set(100)
            self.log_message("🎉 Analyse terminée!")

            # Afficher les résultats
            self.display_analysis_results()

            # Passer à l'onglet résultats
            self.root.after(0, lambda: self.notebook.select(2))

        except Exception as e:
            self.log_message(f"❌ Erreur lors de l'analyse: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de l'analyse: {e}")

        finally:
            self.is_analyzing = False

    def analyze_single_file(self, file_path):
        """Analyse un seul fichier"""
        results = {
            'file_path': file_path,
            'issues': [],
            'stats': {},
            'analysis_types': []
        }

        try:
            # Créer l'agent pour ce fichier
            agent = FRED2000DebugAgent(file_path)

            # Statistiques de base
            results['stats'] = {
                'total_lines': len(agent.code_lines),
                'total_functions': len(agent.functions),
                'file_size': os.path.getsize(file_path)
            }

            all_issues = []

            # Analyse SQL si activée
            if self.sql_analysis_var.get():
                sql_detector = SQLErrorDetector()
                sql_issues = sql_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(sql_issues)
                results['analysis_types'].append('SQL')

            # Analyse mémoire si activée
            if self.memory_analysis_var.get():
                memory_detector = MemoryErrorDetector()
                memory_issues = memory_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(memory_issues)
                results['analysis_types'].append('Mémoire')

            # Analyse logique métier si activée
            if self.business_analysis_var.get():
                business_detector = BusinessLogicDetector()
                business_issues = business_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(business_issues)
                results['analysis_types'].append('Métier')

            # Analyse performance si activée
            if self.performance_analysis_var.get():
                performance_detector = PerformanceDetector()
                performance_issues = performance_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(performance_issues)
                results['analysis_types'].append('Performance')

            # Analyse statique si activée
            if self.static_analysis_var.get():
                static_analyzer = FRED2000StaticAnalyzer()
                static_issues = static_analyzer.analyze_code(agent.code_lines)
                all_issues.extend(static_issues)
                results['analysis_types'].append('Statique')

            # Convertir les issues en dictionnaires pour JSON
            results['issues'] = [
                {
                    'type': issue.error_type.value,
                    'severity': issue.severity.value,
                    'line': issue.line_number,
                    'function': issue.function_name,
                    'description': issue.description,
                    'code': issue.code_snippet,
                    'suggestion': issue.suggestion
                }
                for issue in all_issues
            ]

        except Exception as e:
            self.log_message(f"⚠ Erreur lors de l'analyse de {os.path.basename(file_path)}: {e}")
            results['error'] = str(e)

        return results

    def display_analysis_results(self):
        """Affiche les résultats d'analyse"""
        # Vider la zone de résultats
        self.results_text.delete(1.0, tk.END)

        if not self.analysis_results:
            self.results_text.insert(tk.END, "Aucun résultat d'analyse disponible.\n")
            return

        # En-tête du rapport
        self.results_text.insert(tk.END, "🔍 RAPPORT D'ANALYSE FRED2000\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.results_text.insert(tk.END, f"📅 Date d'analyse: {timestamp}\n")
        self.results_text.insert(tk.END, f"📁 Dossier: {self.current_folder}\n")
        self.results_text.insert(tk.END, f"📄 Fichiers analysés: {len(self.analysis_results)}\n\n")

        # Résumé global
        total_issues = sum(len(result['issues']) for result in self.analysis_results.values())
        self.results_text.insert(tk.END, f"🚨 TOTAL PROBLÈMES DÉTECTÉS: {total_issues}\n\n")

        # Statistiques par gravité
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        type_counts = {}

        for result in self.analysis_results.values():
            for issue in result['issues']:
                severity = issue['severity']
                issue_type = issue['type']

                severity_counts[severity] = severity_counts.get(severity, 0) + 1
                type_counts[issue_type] = type_counts.get(issue_type, 0) + 1

        self.results_text.insert(tk.END, "📊 RÉPARTITION PAR GRAVITÉ:\n")
        for severity, count in severity_counts.items():
            if count > 0:
                emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🔵"}.get(severity, "⚪")
                self.results_text.insert(tk.END, f"   {emoji} {severity.upper()}: {count}\n")

        self.results_text.insert(tk.END, "\n📋 RÉPARTITION PAR TYPE:\n")
        for issue_type, count in sorted(type_counts.items()):
            self.results_text.insert(tk.END, f"   • {issue_type.replace('_', ' ').title()}: {count}\n")

        # Détails par fichier
        self.results_text.insert(tk.END, "\n" + "=" * 50 + "\n")
        self.results_text.insert(tk.END, "📄 DÉTAILS PAR FICHIER\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")

        for file_path, result in self.analysis_results.items():
            file_name = os.path.basename(file_path)
            issues = result['issues']
            stats = result.get('stats', {})

            self.results_text.insert(tk.END, f"📄 {file_name}\n")
            self.results_text.insert(tk.END, f"   Lignes: {stats.get('total_lines', 'N/A')}\n")
            self.results_text.insert(tk.END, f"   Fonctions: {stats.get('total_functions', 'N/A')}\n")
            self.results_text.insert(tk.END, f"   Problèmes: {len(issues)}\n")

            if 'error' in result:
                self.results_text.insert(tk.END, f"   ❌ Erreur: {result['error']}\n")

            # Top 5 problèmes pour ce fichier
            if issues:
                self.results_text.insert(tk.END, f"\n   🔍 Top 5 problèmes:\n")
                for i, issue in enumerate(issues[:5], 1):
                    severity_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🔵"}.get(issue['severity'], "⚪")
                    self.results_text.insert(tk.END, f"   {i}. {severity_emoji} Ligne {issue['line']} - {issue['description']}\n")

                if len(issues) > 5:
                    self.results_text.insert(tk.END, f"      ... et {len(issues) - 5} autres problèmes\n")

            self.results_text.insert(tk.END, "\n" + "-" * 40 + "\n\n")

        # Recommandations
        self.results_text.insert(tk.END, "💡 RECOMMANDATIONS:\n")
        self.results_text.insert(tk.END, "=" * 20 + "\n")

        if total_issues == 0:
            self.results_text.insert(tk.END, "✅ Excellent! Aucun problème détecté.\n")
        elif total_issues < 10:
            self.results_text.insert(tk.END, "🟢 Bon état général. Quelques améliorations mineures possibles.\n")
        elif total_issues < 50:
            self.results_text.insert(tk.END, "🟡 État correct. Recommandé de corriger les problèmes de haute priorité.\n")
        else:
            self.results_text.insert(tk.END, "🔴 Attention! Nombreux problèmes détectés. Révision approfondie recommandée.\n")

        # Conseils spécifiques
        if severity_counts['critical'] > 0:
            self.results_text.insert(tk.END, f"🚨 {severity_counts['critical']} problème(s) critique(s) à corriger en priorité!\n")

        if 'sql_error' in type_counts:
            self.results_text.insert(tk.END, "🗄️ Vérifiez la gestion d'erreurs SQL et les transactions.\n")

        if 'buffer_overflow' in type_counts:
            self.results_text.insert(tk.END, "🛡️ Attention aux fonctions dangereuses (strcpy, sprintf, etc.).\n")

        if 'performance' in type_counts:
            self.results_text.insert(tk.END, "⚡ Optimisez les boucles contenant des requêtes SQL.\n")

    def export_html_report(self):
        """Exporte un rapport HTML"""
        if not self.analysis_results:
            messagebox.showwarning("Attention", "Aucune analyse disponible. Lancez d'abord une analyse.")
            return

        try:
            from report_generator import FRED2000ReportGenerator

            generator = FRED2000ReportGenerator()
            output_file = generator.generate_html_report(self.analysis_results)

            self.log_message(f"📄 Rapport HTML généré: {output_file}")
            messagebox.showinfo("Succès", f"Rapport HTML généré:\n{output_file}")

            # Actualiser la liste des rapports
            self.refresh_reports_list()

            # Demander si ouvrir le rapport
            if messagebox.askyesno("Ouvrir le rapport", "Voulez-vous ouvrir le rapport maintenant?"):
                os.startfile(output_file)

        except Exception as e:
            self.log_message(f"❌ Erreur génération HTML: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la génération HTML: {e}")

    def export_json_report(self):
        """Exporte un rapport JSON"""
        if not self.analysis_results:
            messagebox.showwarning("Attention", "Aucune analyse disponible. Lancez d'abord une analyse.")
            return

        try:
            from report_generator import FRED2000ReportGenerator

            generator = FRED2000ReportGenerator()
            output_file = generator.generate_json_report(self.analysis_results)

            self.log_message(f"📊 Rapport JSON généré: {output_file}")
            messagebox.showinfo("Succès", f"Rapport JSON généré:\n{output_file}")

            # Actualiser la liste des rapports
            self.refresh_reports_list()

        except Exception as e:
            self.log_message(f"❌ Erreur génération JSON: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la génération JSON: {e}")

    def export_csv_report(self):
        """Exporte un rapport CSV"""
        if not self.analysis_results:
            messagebox.showwarning("Attention", "Aucune analyse disponible. Lancez d'abord une analyse.")
            return

        try:
            from report_generator import FRED2000ReportGenerator

            generator = FRED2000ReportGenerator()
            output_file = generator.generate_csv_report(self.analysis_results)

            self.log_message(f"📋 Rapport CSV généré: {output_file}")
            messagebox.showinfo("Succès", f"Rapport CSV généré:\n{output_file}")

            # Actualiser la liste des rapports
            self.refresh_reports_list()

        except Exception as e:
            self.log_message(f"❌ Erreur génération CSV: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la génération CSV: {e}")

    def refresh_reports_list(self):
        """Actualise la liste des rapports générés"""
        # Vider la liste
        for item in self.reports_tree.get_children():
            self.reports_tree.delete(item)

        # Chercher les fichiers de rapport
        import glob

        patterns = [
            "fred2000_report_*.html",
            "fred2000_report_*.json",
            "fred2000_issues_*.csv"
        ]

        for pattern in patterns:
            files = glob.glob(pattern)
            for file_path in files:
                try:
                    stat = os.stat(file_path)
                    size = self.format_file_size(stat.st_size)
                    modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

                    # Déterminer le type
                    if file_path.endswith('.html'):
                        file_type = "📄 HTML"
                    elif file_path.endswith('.json'):
                        file_type = "📊 JSON"
                    elif file_path.endswith('.csv'):
                        file_type = "📋 CSV"
                    else:
                        file_type = "📄 Fichier"

                    # Ajouter à la liste
                    item_id = self.reports_tree.insert('', tk.END, values=(file_type, modified, size))
                    self.reports_tree.set(item_id, 'file_path', file_path)

                except Exception as e:
                    self.log_message(f"⚠ Erreur avec le rapport {file_path}: {e}")

    def open_selected_report(self):
        """Ouvre le rapport sélectionné"""
        selection = self.reports_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un rapport à ouvrir")
            return

        item = selection[0]
        file_path = self.reports_tree.set(item, 'file_path')

        try:
            os.startfile(file_path)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir le fichier: {e}")

    def open_reports_folder(self):
        """Ouvre le dossier contenant les rapports"""
        try:
            os.startfile(os.getcwd())
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir le dossier: {e}")

    def run(self):
        """Lance l'interface graphique"""
        self.root.mainloop()

def main():
    """Point d'entrée principal"""
    try:
        app = FRED2000GUI()
        app.run()
    except Exception as e:
        print(f"Erreur lors du lancement de l'interface: {e}")
        messagebox.showerror("Erreur", f"Erreur lors du lancement: {e}")

if __name__ == "__main__":
    main()