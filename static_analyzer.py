#!/usr/bin/env python3
"""
Analyseur statique pour le code FRED2000
Analyse le code sans l'exécuter pour détecter les problèmes potentiels

Auteur: Assistant IA
Date: 2025-01-14
"""

import re
import ast
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from fred2000_debug_agent import DebugIssue, ErrorType, Severity

@dataclass
class Variable:
    """Représente une variable dans le code"""
    name: str
    type: str
    line_declared: int
    is_initialized: bool = False
    is_used: bool = False
    scope: str = "local"

@dataclass
class Function:
    """Représente une fonction dans le code"""
    name: str
    line_start: int
    line_end: int
    parameters: List[str]
    return_type: str
    calls: List[str]  # Fonctions appelées
    variables: List[Variable]

class FRED2000StaticAnalyzer:
    """Analyseur statique spécialisé pour FRED2000"""
    
    def __init__(self):
        self.functions: Dict[str, Function] = {}
        self.global_variables: List[Variable] = {}
        self.issues: List[DebugIssue] = []
        
        # Patterns spécifiques à FRED2000
        self.fred_patterns = {
            'mask_calls': r'D\d+000_maske_\d+\(',
            'sql_exec': r'EXEC\s+SQL',
            'error_returns': r'return\s+(ERROR|NOT_OK)',
            'logging_calls': r'(log_ent|puterrdb|op_fatal_msge)\s*\(',
            'memory_alloc': r'(malloc|calloc|free)\s*\(',
            'string_funcs': r'(strcpy|strcat|sprintf|strlen)\s*\('
        }
        
        # Types de données FRED2000
        self.fred_types = {
            'string': r'string\s+(\w+)\s*\[(\d+)\]',
            'long': r'long\s+(\w+)',
            'int': r'int\s+(\w+)',
            'char': r'char\s+(\w+)\s*\[(\d+)\]',
            'double': r'double\s+(\w+)'
        }
    
    def analyze_code(self, code_lines: List[str]) -> List[DebugIssue]:
        """Lance l'analyse statique complète"""
        print("🔍 Analyse statique du code FRED2000...")
        
        self.issues.clear()
        self._parse_functions(code_lines)
        self._parse_variables(code_lines)
        self._analyze_control_flow(code_lines)
        self._analyze_data_flow(code_lines)
        self._analyze_fred_specific_patterns(code_lines)
        
        return self.issues
    
    def _parse_functions(self, code_lines: List[str]):
        """Parse et analyse toutes les fonctions"""
        current_function = None
        brace_count = 0
        
        for i, line in enumerate(code_lines):
            line_clean = line.strip()
            
            # Détection de début de fonction
            func_match = re.match(r'^(int|void|char|long|double|static)\s+(\w+)\s*\(([^)]*)\)', line_clean)
            if func_match:
                if current_function:
                    current_function.line_end = i - 1
                    self.functions[current_function.name] = current_function
                
                return_type, func_name, params = func_match.groups()
                parameters = [p.strip().split()[-1] for p in params.split(',') if p.strip()]
                
                current_function = Function(
                    name=func_name,
                    line_start=i + 1,
                    line_end=-1,
                    parameters=parameters,
                    return_type=return_type,
                    calls=[],
                    variables=[]
                )
                brace_count = 0
            
            # Comptage des accolades
            if current_function:
                brace_count += line_clean.count('{') - line_clean.count('}')
                
                # Détection des appels de fonction
                func_calls = re.findall(r'(\w+)\s*\(', line_clean)
                current_function.calls.extend(func_calls)
                
                if brace_count == 0 and '}' in line_clean and i > current_function.line_start:
                    current_function.line_end = i + 1
                    self.functions[current_function.name] = current_function
                    current_function = None
    
    def _parse_variables(self, code_lines: List[str]):
        """Parse et analyse les variables"""
        for i, line in enumerate(code_lines):
            line_clean = line.strip()
            
            # Détection des déclarations de variables
            for type_pattern in self.fred_types.values():
                matches = re.findall(type_pattern, line_clean)
                for match in matches:
                    var_name = match[0] if isinstance(match, tuple) else match
                    
                    # Déterminer la fonction contenante
                    func_name = self._get_function_at_line(i + 1)
                    
                    variable = Variable(
                        name=var_name,
                        type=type_pattern,
                        line_declared=i + 1,
                        is_initialized='=' in line_clean,
                        scope=func_name if func_name != "unknown" else "global"
                    )
                    
                    if func_name in self.functions:
                        self.functions[func_name].variables.append(variable)
                    else:
                        self.global_variables.append(variable)
    
    def _analyze_control_flow(self, code_lines: List[str]):
        """Analyse le flux de contrôle"""
        for i, line in enumerate(code_lines):
            line_clean = line.strip()
            line_num = i + 1
            func_name = self._get_function_at_line(line_num)
            
            # 1. Return sans valeur dans fonction non-void
            if re.search(r'^return\s*;', line_clean):
                if func_name in self.functions and self.functions[func_name].return_type != 'void':
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=func_name,
                        description="Return sans valeur dans fonction non-void",
                        code_snippet=line_clean,
                        suggestion=f"Retourner une valeur appropriée pour le type {self.functions[func_name].return_type}"
                    ))
            
            # 2. Conditions toujours vraies/fausses
            if re.search(r'if\s*\(\s*(0|1|true|false)\s*\)', line_clean):
                self.issues.append(DebugIssue(
                    error_type=ErrorType.LOGIC_ERROR,
                    severity=Severity.LOW,
                    line_number=line_num,
                    function_name=func_name,
                    description="Condition constante détectée",
                    code_snippet=line_clean,
                    suggestion="Vérifier si cette condition est intentionnelle"
                ))
            
            # 3. Boucles infinies potentielles
            if re.search(r'while\s*\(\s*1\s*\)', line_clean):
                if not self._has_break_in_loop(code_lines, i):
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.HIGH,
                        line_number=line_num,
                        function_name=func_name,
                        description="Boucle infinie potentielle sans break",
                        code_snippet=line_clean,
                        suggestion="Ajouter une condition de sortie ou un break"
                    ))
    
    def _analyze_data_flow(self, code_lines: List[str]):
        """Analyse le flux de données"""
        for func_name, function in self.functions.items():
            func_lines = code_lines[function.line_start-1:function.line_end]
            
            # 1. Variables déclarées mais non utilisées
            for variable in function.variables:
                if not self._is_variable_used(func_lines, variable.name):
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.LOW,
                        line_number=variable.line_declared,
                        function_name=func_name,
                        description=f"Variable '{variable.name}' déclarée mais non utilisée",
                        code_snippet=f"Variable: {variable.name}",
                        suggestion="Supprimer la variable ou l'utiliser"
                    ))
            
            # 2. Variables utilisées sans initialisation
            for i, line in enumerate(func_lines):
                line_clean = line.strip()
                
                # Chercher les utilisations de variables
                var_uses = re.findall(r'\b(\w+)\b', line_clean)
                for var_use in var_uses:
                    var = self._find_variable(function.variables, var_use)
                    if var and not var.is_initialized and '=' not in line_clean:
                        self.issues.append(DebugIssue(
                            error_type=ErrorType.LOGIC_ERROR,
                            severity=Severity.MEDIUM,
                            line_number=function.line_start + i,
                            function_name=func_name,
                            description=f"Variable '{var_use}' utilisée sans initialisation",
                            code_snippet=line_clean,
                            suggestion=f"Initialiser {var_use} avant utilisation"
                        ))
    
    def _analyze_fred_specific_patterns(self, code_lines: List[str]):
        """Analyse les patterns spécifiques à FRED2000"""
        for i, line in enumerate(code_lines):
            line_clean = line.strip()
            line_num = i + 1
            func_name = self._get_function_at_line(line_num)
            
            # 1. Appels de masques sans gestion d'erreur
            if re.search(self.fred_patterns['mask_calls'], line_clean):
                if not self._has_status_check(code_lines, i):
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.BUSINESS_RULE,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=func_name,
                        description="Appel de masque FRED sans vérification de status",
                        code_snippet=line_clean,
                        suggestion="Vérifier le status retourné: if (status == ERROR) { ... }"
                    ))
            
            # 2. Requêtes SQL sans transaction
            if re.search(self.fred_patterns['sql_exec'], line_clean):
                if 'INSERT' in line or 'UPDATE' in line or 'DELETE' in line:
                    if not self._is_in_transaction(code_lines, i):
                        self.issues.append(DebugIssue(
                            error_type=ErrorType.SQL_ERROR,
                            severity=Severity.MEDIUM,
                            line_number=line_num,
                            function_name=func_name,
                            description="Requête SQL de modification sans transaction explicite",
                            code_snippet=line_clean,
                            suggestion="Encapsuler dans BEGIN/COMMIT transaction"
                        ))
            
            # 3. Gestion d'erreur FRED2000 incomplète
            if re.search(self.fred_patterns['error_returns'], line_clean):
                if not self._has_fred_error_logging(code_lines, i):
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.BUSINESS_RULE,
                        severity=Severity.LOW,
                        line_number=line_num,
                        function_name=func_name,
                        description="Return ERROR sans logging FRED approprié",
                        code_snippet=line_clean,
                        suggestion="Ajouter log_ent() ou puterrdb() avant return"
                    ))
    
    def _get_function_at_line(self, line_num: int) -> str:
        """Retourne le nom de la fonction à la ligne donnée"""
        for func_name, function in self.functions.items():
            if function.line_start <= line_num <= function.line_end:
                return func_name
        return "unknown"
    
    def _has_break_in_loop(self, code_lines: List[str], loop_start: int) -> bool:
        """Vérifie si une boucle contient un break"""
        brace_count = 0
        for i in range(loop_start, min(len(code_lines), loop_start + 50)):
            line = code_lines[i].strip()
            brace_count += line.count('{') - line.count('}')
            
            if 'break' in line:
                return True
            
            if brace_count == 0 and i > loop_start:
                break
        
        return False
    
    def _is_variable_used(self, func_lines: List[str], var_name: str) -> bool:
        """Vérifie si une variable est utilisée dans la fonction"""
        for line in func_lines:
            if re.search(rf'\b{var_name}\b', line) and f'{var_name}[' not in line:
                return True
        return False
    
    def _find_variable(self, variables: List[Variable], var_name: str) -> Optional[Variable]:
        """Trouve une variable par son nom"""
        for var in variables:
            if var.name == var_name:
                return var
        return None
    
    def _has_status_check(self, code_lines: List[str], call_line: int) -> bool:
        """Vérifie si le status est vérifié après un appel"""
        check_lines = code_lines[call_line+1:call_line+5]
        return any('status' in line and ('==' in line or '!=' in line) for line in check_lines)
    
    def _is_in_transaction(self, code_lines: List[str], sql_line: int) -> bool:
        """Vérifie si la requête SQL est dans une transaction"""
        # Chercher BEGIN en arrière
        for i in range(max(0, sql_line-20), sql_line):
            if 'BEGIN' in code_lines[i] and 'WORK' in code_lines[i]:
                return True
        return False
    
    def _has_fred_error_logging(self, code_lines: List[str], error_line: int) -> bool:
        """Vérifie si un logging FRED précède l'erreur"""
        prev_lines = code_lines[max(0, error_line-3):error_line]
        return any(
            re.search(self.fred_patterns['logging_calls'], line)
            for line in prev_lines
        )
    
    def generate_complexity_report(self) -> Dict:
        """Génère un rapport de complexité du code"""
        report = {
            "total_functions": len(self.functions),
            "total_lines": sum(f.line_end - f.line_start for f in self.functions.values()),
            "average_function_length": 0,
            "complex_functions": [],
            "function_call_graph": {}
        }
        
        if self.functions:
            report["average_function_length"] = report["total_lines"] / len(self.functions)
            
            # Fonctions complexes (> 100 lignes)
            for name, func in self.functions.items():
                length = func.line_end - func.line_start
                if length > 100:
                    report["complex_functions"].append({
                        "name": name,
                        "length": length,
                        "parameters": len(func.parameters),
                        "calls": len(set(func.calls))
                    })
                
                # Graphe d'appels
                report["function_call_graph"][name] = list(set(func.calls))
        
        return report
