@echo off
echo ============================================================
echo    Debug Agent - Application Web
echo    Interface Web Moderne pour l'Analyse des Fichiers .ec
echo    Version 1.0 - 2025-01-14
echo ============================================================
echo.

REM Changer vers le repertoire du script
cd /d "%~dp0"

echo Verification de Python...

REM Essayer python
python --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python
    goto :install_deps
)

REM Essayer python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python3
    goto :install_deps
)

REM Essayer py
py --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=py
    goto :install_deps
)

REM Python non trouve
echo.
echo ERREUR: Python n'est pas installe ou non accessible.
echo.
echo Solutions:
echo 1. Installez Python depuis https://python.org
echo 2. Ou installez depuis le Microsoft Store
echo 3. Verifiez que Python est dans le PATH
echo.
echo Appuyez sur une touche pour ouvrir le site Python...
pause >nul
start https://python.org/downloads/
goto :end

:install_deps
echo Python trouve: %PYTHON_CMD%
echo.
echo Installation des dependances...
%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ERREUR: Impossible d'installer les dependances.
    echo Verifiez votre connexion internet et les permissions.
    pause
    goto :end
)

echo.
echo Dependances installees avec succes!
echo.
echo ============================================================
echo    Lancement de l'application web...
echo    Interface disponible sur: http://localhost:5000
echo ============================================================
echo.

REM Ouvrir le navigateur apres un delai
start "" cmd /c "timeout /t 3 >nul && start http://localhost:5000"

REM Lancer l'application
%PYTHON_CMD% app.py

:end
echo.
echo Application fermee.
echo Appuyez sur une touche pour fermer...
pause >nul
