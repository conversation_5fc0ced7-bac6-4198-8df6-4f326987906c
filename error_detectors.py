#!/usr/bin/env python3
"""
Modules de détection d'erreurs spécialisés pour FRED2000
Chaque module se concentre sur un type d'erreur spécifique

Auteur: Assistant IA
Date: 2025-01-14
"""

import re
from typing import List, Dict, Tuple
from fred2000_debug_agent import DebugIssue, ErrorType, Severity

class SQLErrorDetector:
    """Détecteur spécialisé pour les erreurs SQL/ESQL"""
    
    def __init__(self):
        self.sql_keywords = [
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'EXEC SQL',
            'DECLARE', 'OPEN', 'FETCH', 'CLOSE'
        ]
        
        self.error_codes = {
            'DUPLICATE_KEY': -239,
            'TABLE_LOCKED': -289,
            'MORE_THAN_ONE': -284,
            'NOTFOUND': 2
        }
    
    def detect_issues(self, code_lines: List[str], get_function_at_line) -> List[DebugIssue]:
        """Détecte les problèmes SQL"""
        issues = []
        
        for i, line in enumerate(code_lines):
            line_num = i + 1
            line_clean = line.strip()
            
            # 1. Requêtes SQL sans gestion d'erreur
            if re.search(r'EXEC\s+SQL', line, re.IGNORECASE):
                if not self._has_error_handling(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.SQL_ERROR,
                        severity=Severity.HIGH,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="Requête SQL sans gestion d'erreur",
                        code_snippet=line_clean,
                        suggestion="Ajouter: if (sqlca.sqlcode != 0) { puterrdb(); return ERROR; }"
                    ))
            
            # 2. Curseurs non fermés
            if re.search(r'EXEC\s+SQL\s+OPEN', line, re.IGNORECASE):
                cursor_name = self._extract_cursor_name(line)
                if cursor_name and not self._has_cursor_close(code_lines, i, cursor_name):
                    issues.append(DebugIssue(
                        error_type=ErrorType.SQL_ERROR,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description=f"Curseur '{cursor_name}' ouvert mais jamais fermé",
                        code_snippet=line_clean,
                        suggestion=f"Ajouter: EXEC SQL CLOSE {cursor_name};"
                    ))
            
            # 3. Variables non initialisées dans les requêtes
            if 'sqlca.sqlcode' in line and '=' not in line:
                if not self._has_sqlcode_init(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.SQL_ERROR,
                        severity=Severity.LOW,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="sqlca.sqlcode utilisé sans initialisation",
                        code_snippet=line_clean,
                        suggestion="Initialiser sqlca.sqlcode = 0 avant utilisation"
                    ))
        
        return issues
    
    def _has_error_handling(self, code_lines: List[str], sql_line_index: int) -> bool:
        """Vérifie si une gestion d'erreur suit la requête SQL"""
        check_lines = code_lines[sql_line_index+1:sql_line_index+5]
        return any(
            'sqlca.sqlcode' in line or 'puterrdb' in line or 'if' in line
            for line in check_lines
        )
    
    def _extract_cursor_name(self, line: str) -> str:
        """Extrait le nom du curseur d'une ligne OPEN"""
        match = re.search(r'OPEN\s+(\w+)', line, re.IGNORECASE)
        return match.group(1) if match else ""
    
    def _has_cursor_close(self, code_lines: List[str], open_line_index: int, cursor_name: str) -> bool:
        """Vérifie si le curseur est fermé dans la fonction"""
        remaining_lines = code_lines[open_line_index:]
        close_pattern = rf'CLOSE\s+{cursor_name}'
        return any(re.search(close_pattern, line, re.IGNORECASE) for line in remaining_lines)
    
    def _has_sqlcode_init(self, code_lines: List[str], check_line_index: int) -> bool:
        """Vérifie si sqlca.sqlcode est initialisé avant utilisation"""
        prev_lines = code_lines[max(0, check_line_index-10):check_line_index]
        return any('sqlca.sqlcode = 0' in line for line in prev_lines)

class MemoryErrorDetector:
    """Détecteur spécialisé pour les erreurs de gestion mémoire"""
    
    def __init__(self):
        self.dangerous_functions = {
            'strcpy': 'strncpy',
            'strcat': 'strncat', 
            'sprintf': 'snprintf',
            'gets': 'fgets',
            'scanf': 'utiliser des limites de taille'
        }
    
    def detect_issues(self, code_lines: List[str], get_function_at_line) -> List[DebugIssue]:
        """Détecte les problèmes de gestion mémoire"""
        issues = []
        
        for i, line in enumerate(code_lines):
            line_num = i + 1
            line_clean = line.strip()
            
            # 1. Fonctions dangereuses
            for dangerous_func, safe_alternative in self.dangerous_functions.items():
                if re.search(rf'\b{dangerous_func}\s*\(', line):
                    severity = Severity.HIGH if dangerous_func in ['strcpy', 'gets'] else Severity.MEDIUM
                    issues.append(DebugIssue(
                        error_type=ErrorType.BUFFER_OVERFLOW,
                        severity=severity,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description=f"Utilisation de {dangerous_func}() - risque de buffer overflow",
                        code_snippet=line_clean,
                        suggestion=f"Remplacer par {safe_alternative}()"
                    ))
            
            # 2. Allocation mémoire sans vérification
            if re.search(r'\b(malloc|calloc)\s*\(', line):
                if not self._has_null_check(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.NULL_POINTER,
                        severity=Severity.HIGH,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="Allocation mémoire sans vérification NULL",
                        code_snippet=line_clean,
                        suggestion="Ajouter: if (ptr == NULL) { return ERROR; }"
                    ))
            
            # 3. Débordement de buffer potentiel
            if re.search(r'char\s+\w+\[\d+\]', line):
                buffer_size = self._extract_buffer_size(line)
                if buffer_size and buffer_size < 256:  # Buffers petits
                    issues.append(DebugIssue(
                        error_type=ErrorType.BUFFER_OVERFLOW,
                        severity=Severity.LOW,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description=f"Buffer de petite taille ({buffer_size}) - risque de débordement",
                        code_snippet=line_clean,
                        suggestion="Considérer augmenter la taille ou utiliser allocation dynamique"
                    ))
        
        return issues
    
    def _has_null_check(self, code_lines: List[str], alloc_line_index: int) -> bool:
        """Vérifie si une vérification NULL suit l'allocation"""
        check_lines = code_lines[alloc_line_index+1:alloc_line_index+3]
        return any('== NULL' in line or '!= NULL' in line for line in check_lines)
    
    def _extract_buffer_size(self, line: str) -> int:
        """Extrait la taille d'un buffer depuis sa déclaration"""
        match = re.search(r'\[(\d+)\]', line)
        return int(match.group(1)) if match else 0

class BusinessLogicDetector:
    """Détecteur spécialisé pour la logique métier FRED2000"""
    
    def __init__(self):
        self.fred_error_codes = {
            'ERROR': -1,
            'NOT_OK': 1,
            'NOTFOUND': 2,
            'DUPLICATE_KEY': -239,
            'TABLE_LOCKED': -289
        }
        
        self.critical_functions = [
            'D1000_maske_01', 'D4000_maske_04', 'D5000_maske_05',
            'FRED_get_fredparm', 'A100_program_anf'
        ]
    
    def detect_issues(self, code_lines: List[str], get_function_at_line) -> List[DebugIssue]:
        """Détecte les problèmes de logique métier"""
        issues = []
        
        for i, line in enumerate(code_lines):
            line_num = i + 1
            line_clean = line.strip()
            
            # 1. Return ERROR sans logging
            if 'return ERROR' in line and 'if' not in line:
                if not self._has_error_logging(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="Return ERROR sans logging d'erreur",
                        code_snippet=line_clean,
                        suggestion="Ajouter logging avant return: log_ent() ou puterrdb()"
                    ))
            
            # 2. Validation des paramètres manquante
            if re.search(r'int\s+\w+\([^)]*\w+.*\)', line):  # Fonction avec paramètres
                func_name = re.search(r'int\s+(\w+)\(', line)
                if func_name and not self._has_param_validation(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.LOW,
                        line_number=line_num,
                        function_name=func_name.group(1),
                        description="Fonction sans validation des paramètres d'entrée",
                        code_snippet=line_clean,
                        suggestion="Ajouter validation des paramètres en début de fonction"
                    ))
            
            # 3. Gestion des masques FRED2000
            if re.search(r'status\s*=\s*MASKE_\d+', line):
                if not self._has_mask_error_handling(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.BUSINESS_RULE,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="Appel de masque sans gestion d'erreur appropriée",
                        code_snippet=line_clean,
                        suggestion="Vérifier le status retourné par le masque"
                    ))
        
        return issues
    
    def _has_error_logging(self, code_lines: List[str], error_line_index: int) -> bool:
        """Vérifie si un logging d'erreur précède le return ERROR"""
        prev_lines = code_lines[max(0, error_line_index-3):error_line_index]
        logging_patterns = ['log_', 'puterrdb', 'op_fatal_msge', 'printf']
        return any(
            any(pattern in line for pattern in logging_patterns)
            for line in prev_lines
        )
    
    def _has_param_validation(self, code_lines: List[str], func_line_index: int) -> bool:
        """Vérifie si la fonction valide ses paramètres"""
        check_lines = code_lines[func_line_index+1:func_line_index+10]
        validation_patterns = ['if', 'return ERROR', 'NULL', '== 0', '!= 0']
        return any(
            any(pattern in line for pattern in validation_patterns)
            for line in check_lines
        )
    
    def _has_mask_error_handling(self, code_lines: List[str], mask_line_index: int) -> bool:
        """Vérifie si l'appel de masque a une gestion d'erreur"""
        next_lines = code_lines[mask_line_index+1:mask_line_index+5]
        return any(
            'if' in line and ('status' in line or 'ERROR' in line)
            for line in next_lines
        )

class PerformanceDetector:
    """Détecteur spécialisé pour les problèmes de performance"""
    
    def detect_issues(self, code_lines: List[str], get_function_at_line) -> List[DebugIssue]:
        """Détecte les problèmes de performance"""
        issues = []
        
        for i, line in enumerate(code_lines):
            line_num = i + 1
            line_clean = line.strip()
            
            # 1. Boucles avec requêtes SQL
            if re.search(r'(for|while)\s*\(', line):
                if self._has_sql_in_loop(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.PERFORMANCE,
                        severity=Severity.HIGH,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="Requête SQL dans une boucle - problème de performance",
                        code_snippet=line_clean,
                        suggestion="Optimiser en utilisant une seule requête avec JOIN ou WHERE IN"
                    ))
            
            # 2. Fonctions string répétitives
            if re.search(r'strlen\s*\(.*\)', line):
                if self._is_in_loop_context(code_lines, i):
                    issues.append(DebugIssue(
                        error_type=ErrorType.PERFORMANCE,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=get_function_at_line(line_num),
                        description="strlen() appelé dans une boucle",
                        code_snippet=line_clean,
                        suggestion="Stocker la longueur dans une variable avant la boucle"
                    ))
        
        return issues
    
    def _has_sql_in_loop(self, code_lines: List[str], loop_line_index: int) -> bool:
        """Vérifie si une boucle contient des requêtes SQL"""
        # Chercher la fin de la boucle (approximatif)
        brace_count = 0
        for i in range(loop_line_index, min(len(code_lines), loop_line_index + 50)):
            line = code_lines[i]
            brace_count += line.count('{') - line.count('}')
            
            if 'EXEC SQL' in line:
                return True
            
            if brace_count == 0 and i > loop_line_index:
                break
        
        return False
    
    def _is_in_loop_context(self, code_lines: List[str], line_index: int) -> bool:
        """Vérifie si la ligne est dans un contexte de boucle"""
        # Chercher en arrière pour trouver une boucle
        for i in range(max(0, line_index - 20), line_index):
            if re.search(r'(for|while)\s*\(', code_lines[i]):
                return True
        return False
