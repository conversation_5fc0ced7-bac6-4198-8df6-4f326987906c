update program:  /source/sources_im-i-n/fors_neu/prog/fred2000/fvbco020
from: lxhv1forsdev01.leoni.local (WERK:3, DB:w03@50030)
to  : lxhv1forsdev01.leoni.local
by  : cont7286   [Tue Nov  8 11:53:20 CET 2022]
----------------------------------------------
fvlstand: progdb_unload: FVBCO020
unload table: fvlstand to /source/forscobdev/install/leo20221108/fvbco020_files/fvlstand.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fvlstand WHERE vernam like 'FVBCO020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/fvlstand.out' WITH DELIMITER '|' NULL AS ''   
fleoftml: progdb_unload: FVBCO020
unload table: fleoftml to /source/forscobdev/install/leo20221108/fvbco020_files/fleoftml.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fleoftml WHERE progid like 'FVBCO020%' or progid in ('GENERAL', 'ALLGEMEIN', 'DATE', 'SYSTEM', 'PSMS')) TO '/source/forscobdev/install/leo20221108/fvbco020_files/fleoftml.out' WITH DELIMITER '|' NULL AS ''   
progstart: progdb_unload: FVBCO020
unload table: progstart to /source/forscobdev/install/leo20221108/fvbco020_files/progstart.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM progstart WHERE progname like 'FVBCO020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/progstart.out' WITH DELIMITER '|' NULL AS ''   
progregex: progdb_unload: FVBCO020
unload table: progregex to /source/forscobdev/install/leo20221108/fvbco020_files/progregex.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM progregex ) TO '/source/forscobdev/install/leo20221108/fvbco020_files/progregex.out' WITH DELIMITER '|' NULL AS ''   

done rinstall_dosql.sh [Tue Nov  8 11:53:20 CET 2022]
fpsms: allmask_unload: FVBMP020 FVBCO020
unload table: fpsmsmap to /source/forscobdev/install/leo20221108/fvbco020_files/fpsmsmap.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fpsmsmap WHERE firmnr=3 and werknr=3 and mapnam like 'FVBMP020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/fpsmsmap.out' WITH DELIMITER '|' NULL AS ''   
unload table: fpsmsmaf to /source/forscobdev/install/leo20221108/fvbco020_files/fpsmsmaf.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fpsmsmaf WHERE firmnr=3 and werknr=3 and mapnam like 'FVBMP020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/fpsmsmaf.out' WITH DELIMITER '|' NULL AS ''   
unload table: fpsmsawf to /source/forscobdev/install/leo20221108/fvbco020_files/fpsmsawf.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fpsmsawf WHERE firmnr=3 and werknr=3 and mapnam like 'FVBMP020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/fpsmsawf.out' WITH DELIMITER '|' NULL AS ''   
fpfstand: allmask_unload: FVBMP020 FVBCO020
unload table: fpfstand to /source/forscobdev/install/leo20221108/fvbco020_files/fpfstand.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fpfstand WHERE firmnr=3 and werknr=3 and pgmnam like 'FVBCO020%') TO '/source/forscobdev/install/leo20221108/fvbco020_files/fpfstand.out' WITH DELIMITER '|' NULL AS ''   
fdtstand: allmask_unload: FVBMP020 FVBCO020
unload table: fdtstand to /source/forscobdev/install/leo20221108/fvbco020_files/fdtstand.out
\set QUIET 1
\i /etc/sysconfig/pgsql/psqlrc.prompt
-- set the prompt
\set PROMPT1 '%n@%[%033[1;32m%m%[%033[m[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
\set PROMPT2 '[%[%033[34m%/%[%033[m]%[%033[1;31m%x%[%033[m%R%# '
--\set PROMPT3 '>> '
\set menu '\i /etc/sysconfig/pgsql/generic-psqlrc'
\unset quiet
  \COPY (SELECT * FROM fdtstand WHERE firmnr=3 and werknr=3) TO '/source/forscobdev/install/leo20221108/fvbco020_files/fdtstand.out' WITH DELIMITER '|' NULL AS ''   

done rinstall_dosql.sh [Tue Nov  8 11:53:20 CET 2022]
update: lxhv1forsdev01.leoni.local (WERK:3, DB:w03, ARCH:linux, INST: -) ..\c
 alive
  mods: prog progdb detec spool_ctrl_n detec_n mask_n
update: lxhv1forsdev01.leoni.local (WERK:3, DB:w03, INST:-) module: prog  [Tue Nov  8 11:53:20 CET 2022]
crtinst leo20221108 --leo bin_linux/fvbco020
INFO: install directory leo20221108 already exists!
INFO: FILELIST updated
INFO: install directory: /source/forscobdev/install/leo20221108
update: lxhv1forsdev01.leoni.local (WERK:3, DB:w03, INST:-) module: progdb  [Tue Nov  8 11:53:31 CET 2022]
update: lxhv1forsdev01.leoni.local (WERK:3, DB:w03, INST:-) module: spool_ctrl_n  [Tue Nov  8 11:53:31 CET 2022]
update: lxhv1forsdev01.leoni.local (WERK:3, DB:w03, INST:-) module: mask_n  [Tue Nov  8 11:53:31 CET 2022]
 done lxhv1forsdev01.leoni.local [Tue Nov  8 11:53:31 CET 2022]
----------------------------------------------
----------------------------------------------
 done  [Tue Nov  8 11:53:31 CET 2022]
----------------------------------------------
