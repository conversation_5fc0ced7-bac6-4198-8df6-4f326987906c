<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Agent - Interface Web</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            animation: fadeInDown 1s;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            animation: fadeInUp 1s;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
            font-weight: 600;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 500px;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s;
        }

        .upload-zone {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 50px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .upload-zone:hover {
            border-color: #5a67d8;
            background: #f7fafc;
        }

        .upload-zone.dragover {
            border-color: #4c51bf;
            background: #edf2f7;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-danger {
            background: #dc3545;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-icon {
            font-size: 1.5em;
            color: #667eea;
            margin-right: 15px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }

        .file-checkbox {
            margin-left: 15px;
            transform: scale(1.2);
        }

        .analysis-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .option-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .option-icon {
            font-size: 1.5em;
            margin-right: 10px;
            color: #667eea;
        }

        .option-title {
            font-weight: 600;
            font-size: 1.1em;
        }

        .option-description {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px 0;
        }

        .log-timestamp {
            color: #a0aec0;
            margin-right: 10px;
        }

        .results-container {
            margin-top: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .stat-card.critical { border-left-color: #dc3545; }
        .stat-card.high { border-left-color: #fd7e14; }
        .stat-card.medium { border-left-color: #ffc107; }
        .stat-card.low { border-left-color: #28a745; }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .file-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .file-results h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .issue-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #dee2e6;
        }

        .issue-item.critical { border-left-color: #dc3545; }
        .issue-item.high { border-left-color: #fd7e14; }
        .issue-item.medium { border-left-color: #ffc107; }
        .issue-item.low { border-left-color: #28a745; }

        .issue-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .issue-severity {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .severity-critical { background: #dc3545; color: white; }
        .severity-high { background: #fd7e14; color: white; }
        .severity-medium { background: #ffc107; color: #212529; }
        .severity-low { background: #28a745; color: white; }

        .issue-line {
            color: #6c757d;
            font-size: 0.9em;
        }

        .issue-description {
            margin-bottom: 10px;
            font-weight: 600;
        }

        .issue-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .issue-suggestion {
            color: #28a745;
            font-style: italic;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            text-align: center;
            padding: 50px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .report-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .analysis-options {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> Debug Agent</h1>
            <p>Interface Web pour l'Analyse des Fichiers .ec</p>
        </div>

        <div class="main-card">
            <div class="tabs">
                <div class="tab active" data-tab="upload">
                    <i class="fas fa-upload"></i> Upload
                </div>
                <div class="tab" data-tab="analyze">
                    <i class="fas fa-search"></i> Analyse
                </div>
                <div class="tab" data-tab="results">
                    <i class="fas fa-chart-bar"></i> Résultats
                </div>
                <div class="tab" data-tab="reports">
                    <i class="fas fa-file-alt"></i> Rapports
                </div>
            </div>

            <!-- Onglet Upload -->
            <div class="tab-content active" id="upload">
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3>Glissez-déposez vos fichiers .ec ou archives ZIP ici</h3>
                    <p>ou cliquez pour sélectionner des fichiers</p>
                    <input type="file" id="fileInput" multiple accept=".ec,.zip" style="display: none;">
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-folder-open"></i> Sélectionner des fichiers
                    </button>
                </div>

                <div id="fileList" class="file-list hidden">
                    <h3><i class="fas fa-list"></i> Fichiers uploadés</h3>
                    <div id="files"></div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn" onclick="proceedToAnalysis()">
                            <i class="fas fa-arrow-right"></i> Continuer vers l'analyse
                        </button>
                    </div>
                </div>
            </div>

            <!-- Onglet Analyse -->
            <div class="tab-content" id="analyze">
                <h2><i class="fas fa-cogs"></i> Configuration de l'analyse</h2>
                
                <div class="analysis-options">
                    <div class="option-card">
                        <div class="option-header">
                            <div class="option-icon"><i class="fas fa-database"></i></div>
                            <div class="option-title">Analyse SQL/ESQL</div>
                            <input type="checkbox" id="sqlAnalysis" checked class="file-checkbox">
                        </div>
                        <div class="option-description">
                            Détecte les erreurs dans les requêtes SQL, curseurs non fermés, gestion d'erreurs manquante
                        </div>
                    </div>

                    <div class="option-card">
                        <div class="option-header">
                            <div class="option-icon"><i class="fas fa-memory"></i></div>
                            <div class="option-title">Analyse Mémoire</div>
                            <input type="checkbox" id="memoryAnalysis" checked class="file-checkbox">
                        </div>
                        <div class="option-description">
                            Identifie les buffer overflows, fuites mémoire, pointeurs null
                        </div>
                    </div>

                    <div class="option-card">
                        <div class="option-header">
                            <div class="option-icon"><i class="fas fa-business-time"></i></div>
                            <div class="option-title">Logique Métier</div>
                            <input type="checkbox" id="businessAnalysis" checked class="file-checkbox">
                        </div>
                        <div class="option-description">
                            Vérifie la logique spécifique à FRED2000, gestion des masques, logging
                        </div>
                    </div>

                    <div class="option-card">
                        <div class="option-header">
                            <div class="option-icon"><i class="fas fa-tachometer-alt"></i></div>
                            <div class="option-title">Performance</div>
                            <input type="checkbox" id="performanceAnalysis" checked class="file-checkbox">
                        </div>
                        <div class="option-description">
                            Détecte les problèmes de performance, requêtes SQL dans les boucles
                        </div>
                    </div>

                    <div class="option-card">
                        <div class="option-header">
                            <div class="option-icon"><i class="fas fa-microscope"></i></div>
                            <div class="option-title">Analyse Statique</div>
                            <input type="checkbox" id="staticAnalysis" class="file-checkbox">
                        </div>
                        <div class="option-description">
                            Analyse approfondie du flux de contrôle et des données (plus lente)
                        </div>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn" id="startAnalysisBtn" onclick="startAnalysis()">
                        <i class="fas fa-play"></i> Démarrer l'analyse
                    </button>
                </div>

                <div class="progress-container hidden" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Préparation...</div>
                </div>

                <div class="log-container hidden" id="logContainer">
                    <div id="logContent"></div>
                </div>
            </div>

            <!-- Onglet Résultats -->
            <div class="tab-content" id="results">
                <div id="resultsContent">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Lancez une analyse pour voir les résultats ici</p>
                    </div>
                </div>
            </div>

            <!-- Onglet Rapports -->
            <div class="tab-content" id="reports">
                <h2><i class="fas fa-file-download"></i> Génération de rapports</h2>
                <p>Exportez les résultats d'analyse dans différents formats :</p>
                
                <div class="report-buttons">
                    <button class="btn" onclick="downloadReport('html')">
                        <i class="fas fa-file-code"></i> Rapport HTML
                    </button>
                    <button class="btn btn-secondary" onclick="downloadReport('json')">
                        <i class="fas fa-file-code"></i> Données JSON
                    </button>
                    <button class="btn btn-success" onclick="downloadReport('csv')">
                        <i class="fas fa-file-csv"></i> Liste CSV
                    </button>
                </div>

                <div style="margin-top: 30px;">
                    <h3>Formats disponibles :</h3>
                    <ul style="margin-top: 15px; line-height: 1.8;">
                        <li><strong>HTML</strong> - Rapport complet avec graphiques et mise en forme</li>
                        <li><strong>JSON</strong> - Données structurées pour intégration avec d'autres outils</li>
                        <li><strong>CSV</strong> - Liste des problèmes pour analyse dans Excel/LibreOffice</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
