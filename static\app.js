// Debug Agent - Frontend JavaScript
// Interface web moderne pour l'analyse des fichiers .ec

class FRED2000App {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.analysisId = null;
        this.uploadedFiles = [];
        this.analysisResults = null;
        
        this.initializeApp();
        this.setupEventListeners();
        this.connectWebSocket();
    }

    initializeApp() {
        console.log('🚀 Initialisation de Debug Agent');
        this.showTab('upload');
    }

    setupEventListeners() {
        // Gestion des onglets
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.showTab(tabName);
            });
        });

        // Upload de fichiers
        const fileInput = document.getElementById('fileInput');
        const uploadZone = document.getElementById('uploadZone');

        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        // Drag & Drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });

        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });
    }

    connectWebSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('✅ WebSocket connecté');
        });

        this.socket.on('disconnect', () => {
            console.log('❌ WebSocket déconnecté');
        });

        this.socket.on('analysis_progress', (data) => {
            this.updateProgress(data);
        });

        this.socket.on('analysis_complete', (data) => {
            this.handleAnalysisComplete(data);
        });
    }

    showTab(tabName) {
        // Masquer tous les onglets
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Afficher l'onglet sélectionné
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(tabName).classList.add('active');
    }

    async handleFileSelect(files) {
        if (files.length === 0) return;

        const formData = new FormData();
        Array.from(files).forEach(file => {
            formData.append('files', file);
        });

        try {
            this.showLoading('Upload en cours...');
            
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                this.sessionId = result.session_id;
                this.uploadedFiles = result.files;
                this.displayUploadedFiles();
                this.showNotification('✅ ' + result.message, 'success');
                
                // Rejoindre la session WebSocket
                if (this.socket) {
                    this.socket.emit('join_session', { session_id: this.sessionId });
                }
            } else {
                this.showNotification('❌ ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Erreur upload:', error);
            this.showNotification('❌ Erreur lors de l\'upload', 'error');
        } finally {
            this.hideLoading();
        }
    }

    displayUploadedFiles() {
        const fileList = document.getElementById('fileList');
        const filesContainer = document.getElementById('files');
        
        filesContainer.innerHTML = '';

        this.uploadedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-icon">
                    <i class="fas fa-file-code"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                </div>
                <input type="checkbox" class="file-checkbox" checked data-file="${file.name}">
            `;
            filesContainer.appendChild(fileItem);
        });

        fileList.classList.remove('hidden');
    }

    formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    proceedToAnalysis() {
        if (!this.sessionId) {
            this.showNotification('❌ Aucune session active', 'error');
            return;
        }
        this.showTab('analyze');
    }

    async startAnalysis() {
        if (!this.sessionId) {
            this.showNotification('❌ Aucun fichier uploadé', 'error');
            return;
        }

        // Récupérer les fichiers sélectionnés
        const selectedFiles = [];
        document.querySelectorAll('.file-checkbox:checked').forEach(checkbox => {
            selectedFiles.push(checkbox.dataset.file);
        });

        if (selectedFiles.length === 0) {
            this.showNotification('❌ Aucun fichier sélectionné', 'error');
            return;
        }

        // Récupérer les options d'analyse
        const options = {
            sql_analysis: document.getElementById('sqlAnalysis').checked,
            memory_analysis: document.getElementById('memoryAnalysis').checked,
            business_analysis: document.getElementById('businessAnalysis').checked,
            performance_analysis: document.getElementById('performanceAnalysis').checked,
            static_analysis: document.getElementById('staticAnalysis').checked
        };

        try {
            const response = await fetch('/api/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    files: selectedFiles,
                    options: options
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.analysisId = result.analysis_id;
                this.showAnalysisProgress();
                this.showNotification('🚀 ' + result.message, 'success');
            } else {
                this.showNotification('❌ ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Erreur analyse:', error);
            this.showNotification('❌ Erreur lors du démarrage de l\'analyse', 'error');
        }
    }

    showAnalysisProgress() {
        document.getElementById('progressContainer').classList.remove('hidden');
        document.getElementById('logContainer').classList.remove('hidden');
        document.getElementById('startAnalysisBtn').disabled = true;
    }

    updateProgress(data) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const logContent = document.getElementById('logContent');

        // Mettre à jour la progression
        if (data.progress !== undefined) {
            progressFill.style.width = data.progress + '%';
        }

        progressText.textContent = data.message;

        // Ajouter au log
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="log-timestamp">[${new Date(data.timestamp).toLocaleTimeString()}]</span>
            ${data.message}
        `;
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }

    handleAnalysisComplete(data) {
        this.analysisResults = data.results;
        this.displayResults(data.results, data.statistics);
        this.showTab('results');
        
        document.getElementById('startAnalysisBtn').disabled = false;
        this.showNotification('🎉 Analyse terminée!', 'success');
    }

    displayResults(results, statistics) {
        const resultsContent = document.getElementById('resultsContent');
        
        let html = `
            <h2><i class="fas fa-chart-bar"></i> Résultats d'analyse</h2>
            
            <div class="stats-grid">
                <div class="stat-card critical">
                    <div class="stat-number">${statistics.severity_counts.critical || 0}</div>
                    <div class="stat-label">🔴 Critique</div>
                </div>
                <div class="stat-card high">
                    <div class="stat-number">${statistics.severity_counts.high || 0}</div>
                    <div class="stat-label">🟠 Élevé</div>
                </div>
                <div class="stat-card medium">
                    <div class="stat-number">${statistics.severity_counts.medium || 0}</div>
                    <div class="stat-label">🟡 Moyen</div>
                </div>
                <div class="stat-card low">
                    <div class="stat-number">${statistics.severity_counts.low || 0}</div>
                    <div class="stat-label">🔵 Faible</div>
                </div>
            </div>

            <div class="results-container">
        `;

        // Résultats par fichier
        Object.entries(results).forEach(([fileName, result]) => {
            const issues = result.issues || [];
            const stats = result.stats || {};

            html += `
                <div class="file-results">
                    <h3><i class="fas fa-file-code"></i> ${fileName}</h3>
                    <p>
                        <strong>Lignes:</strong> ${stats.total_lines || 'N/A'} | 
                        <strong>Fonctions:</strong> ${stats.total_functions || 'N/A'} | 
                        <strong>Problèmes:</strong> ${issues.length}
                    </p>
            `;

            if (issues.length > 0) {
                issues.slice(0, 5).forEach(issue => {
                    html += `
                        <div class="issue-item ${issue.severity}">
                            <div class="issue-header">
                                <span class="issue-severity severity-${issue.severity}">${issue.severity}</span>
                                <span class="issue-line">Ligne ${issue.line} - ${issue.function}()</span>
                            </div>
                            <div class="issue-description">${issue.description}</div>
                            <div class="issue-code">${issue.code}</div>
                            <div class="issue-suggestion">💡 ${issue.suggestion}</div>
                        </div>
                    `;
                });

                if (issues.length > 5) {
                    html += `<p><em>... et ${issues.length - 5} autres problèmes</em></p>`;
                }
            } else {
                html += `<p style="color: #28a745; font-weight: 600;">✅ Aucun problème détecté!</p>`;
            }

            html += `</div>`;
        });

        html += `</div>`;
        resultsContent.innerHTML = html;
    }

    async downloadReport(format) {
        if (!this.analysisId) {
            this.showNotification('❌ Aucune analyse disponible', 'error');
            return;
        }

        try {
            const response = await fetch(`/api/analysis/${this.analysisId}/report/${format}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `fred2000_report_${new Date().toISOString().slice(0, 10)}.${format}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showNotification(`✅ Rapport ${format.toUpperCase()} téléchargé`, 'success');
            } else {
                const error = await response.json();
                this.showNotification('❌ ' + error.error, 'error');
            }
        } catch (error) {
            console.error('Erreur téléchargement:', error);
            this.showNotification('❌ Erreur lors du téléchargement', 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Créer une notification toast
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
        `;

        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            default:
                notification.style.background = '#667eea';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        // Supprimer après 5 secondes
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 5000);
    }

    showLoading(message = 'Chargement...') {
        // Implémentation simple du loading
        console.log('Loading:', message);
    }

    hideLoading() {
        console.log('Loading terminé');
    }
}

// Fonctions globales pour compatibilité avec le HTML
let app;

document.addEventListener('DOMContentLoaded', () => {
    app = new FRED2000App();
});

function proceedToAnalysis() {
    app.proceedToAnalysis();
}

function startAnalysis() {
    app.startAnalysis();
}

function downloadReport(format) {
    app.downloadReport(format);
}

// Ajouter les animations CSS manquantes
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
