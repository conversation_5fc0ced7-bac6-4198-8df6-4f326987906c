<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Agent - Explorateur de <PERSON> (Démo)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .main-layout { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .explorer-card { height: 600px; display: flex; flex-direction: column; }
        .analysis-card { height: 600px; display: flex; flex-direction: column; }
        
        /* Explorateur de fichiers */
        .explorer-header { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .path-display { flex: 1; background: #f8f9fa; padding: 8px 12px; border-radius: 5px; font-family: monospace; font-size: 0.9em; }
        .drives-section { margin-bottom: 15px; }
        .drives-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin-bottom: 15px; }
        .drive-item { background: #f8f9fa; padding: 10px; border-radius: 8px; cursor: pointer; transition: all 0.3s; border: 2px solid transparent; }
        .drive-item:hover { background: #e9ecef; border-color: #667eea; }
        .drive-name { font-weight: 600; margin-bottom: 5px; }
        .drive-size { font-size: 0.8em; color: #6c757d; }
        
        .file-browser { flex: 1; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .file-list { height: 100%; overflow-y: auto; }
        .file-item { display: flex; align-items: center; padding: 8px 12px; border-bottom: 1px solid #f0f0f0; cursor: pointer; transition: all 0.3s; }
        .file-item:hover { background: #f8f9fa; }
        .file-item.selected { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .file-item.ec-file { background: #fff3e0; }
        .file-item.ec-file.selected { background: #ffcc80; }
        .file-icon { margin-right: 10px; font-size: 1.2em; }
        .file-info { flex: 1; }
        .file-name { font-weight: 500; }
        .file-details { font-size: 0.8em; color: #6c757d; }
        .file-checkbox { margin-left: auto; }
        
        /* Section d'analyse */
        .analysis-header { margin-bottom: 20px; }
        .selected-files { background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 20px; max-height: 200px; overflow-y: auto; }
        .selected-file { display: flex; justify-content: space-between; align-items: center; padding: 5px 0; }
        .remove-file { background: #dc3545; color: white; border: none; border-radius: 3px; padding: 2px 8px; cursor: pointer; }
        
        .analysis-options { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px; }
        .option { display: flex; align-items: center; gap: 8px; }
        
        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer; font-size: 16px; font-weight: 600; margin: 5px; transition: all 0.3s; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6c757d; }
        .btn-small { padding: 6px 12px; font-size: 14px; }
        
        .status { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        .results-section { margin-top: 20px; }
        .results-content { background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em; white-space: pre-wrap; }
        
        .demo-notice { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .demo-notice strong { color: #856404; }
        
        .installation-section { background: #e8f6f3; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .installation-section h3 { margin-bottom: 15px; }
        .installation-section ol { margin-left: 20px; line-height: 1.8; }
        
        @media (max-width: 1200px) {
            .main-layout { grid-template-columns: 1fr; }
            .explorer-card, .analysis-card { height: auto; min-height: 400px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Debug Agent</h1>
            <p>Explorateur de Fichiers - Démo Interactive</p>
        </div>

        <div class="demo-notice">
            <strong>📋 Mode Démonstration :</strong> Cette interface montre comment l'explorateur de fichiers fonctionne. 
            Pour utiliser la version complète avec navigation réelle dans votre PC, installez Python et lancez l'application.
        </div>

        <div class="main-layout">
            <!-- Explorateur de fichiers -->
            <div class="card explorer-card">
                <div class="explorer-header">
                    <h2>📁 Explorateur de Fichiers</h2>
                    <button class="btn btn-small" onclick="refreshDemo()">🔄 Actualiser</button>
                </div>
                
                <div class="path-display" id="currentPath">C:\Projets\FRED2000\</div>
                
                <div class="drives-section">
                    <h3>💾 Lecteurs et Raccourcis</h3>
                    <div class="drives-grid" id="drivesGrid">
                        <div class="drive-item" onclick="loadDrive('C:\\')">
                            <div class="drive-name">💾 Lecteur C:</div>
                            <div class="drive-size">500 GB</div>
                        </div>
                        <div class="drive-item" onclick="loadDrive('D:\\')">
                            <div class="drive-name">💾 Lecteur D:</div>
                            <div class="drive-size">1 TB</div>
                        </div>
                        <div class="drive-item" onclick="loadDrive('home')">
                            <div class="drive-name">🏠 Dossier Personnel</div>
                            <div class="drive-size">N/A</div>
                        </div>
                        <div class="drive-item" onclick="loadDrive('desktop')">
                            <div class="drive-name">🖥️ Bureau</div>
                            <div class="drive-size">N/A</div>
                        </div>
                    </div>
                </div>
                
                <div class="file-browser">
                    <div class="file-list" id="fileList">
                        <!-- Contenu généré par JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Section d'analyse -->
            <div class="card analysis-card">
                <div class="analysis-header">
                    <h2>🔍 Analyse des Fichiers</h2>
                </div>
                
                <div class="selected-files" id="selectedFiles">
                    <p style="color: #6c757d; font-style: italic;">Aucun fichier .ec sélectionné</p>
                </div>
                
                <div class="analysis-options">
                    <div class="option">
                        <input type="checkbox" id="sqlAnalysis" checked>
                        <label>🗄️ Analyse SQL/ESQL</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="memoryAnalysis" checked>
                        <label>🧠 Analyse Mémoire</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="businessAnalysis" checked>
                        <label>📊 Logique Métier</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="performanceAnalysis" checked>
                        <label>⚡ Performance</label>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn" onclick="startDemoAnalysis()" id="analyzeBtn" disabled>
                        🚀 Analyser les Fichiers Sélectionnés
                    </button>
                    <button class="btn btn-secondary" onclick="clearSelection()">
                        🗑️ Vider la Sélection
                    </button>
                </div>
                
                <div id="analysisStatus" class="status hidden"></div>
                
                <div class="results-section">
                    <h3>📊 Résultats de Démonstration</h3>
                    <div class="results-content" id="resultsContent">
Sélectionnez des fichiers .ec pour voir un exemple d'analyse...
                    </div>
                </div>
            </div>
        </div>

        <div class="installation-section">
            <h3>🚀 Installation pour Utilisation Réelle</h3>
            <p><strong>Pour naviguer réellement dans votre PC et analyser vos fichiers :</strong></p>
            <ol>
                <li><strong>Installez Python</strong> depuis <a href="https://python.org" target="_blank">python.org</a> ou le Microsoft Store</li>
                <li><strong>Double-cliquez</strong> sur <code>LANCER_EXPLORATEUR.bat</code></li>
                <li><strong>Naviguez</strong> dans tous vos lecteurs et dossiers</li>
                <li><strong>Sélectionnez</strong> vos fichiers .ec où qu'ils soient</li>
                <li><strong>Analysez</strong> et obtenez des rapports détaillés</li>
            </ol>
            <p style="margin-top: 15px;">
                <strong>Avantages de la version complète :</strong><br>
                ✅ Navigation complète dans votre PC<br>
                ✅ Analyse réelle des fichiers .ec<br>
                ✅ Rapports HTML professionnels<br>
                ✅ Export en multiple formats
            </p>
        </div>
    </div>

    <script>
        let selectedFiles = new Set();
        let currentLocation = 'C:\\Projets\\FRED2000\\';

        // Données de démonstration
        const demoData = {
            'C:\\': [
                { name: '.. (Dossier parent)', type: 'parent', icon: '⬆️' },
                { name: 'Program Files', type: 'directory', icon: '📁', size: '<DIR>', modified: '2024-01-15 10:30' },
                { name: 'Users', type: 'directory', icon: '📁', size: '<DIR>', modified: '2024-01-10 14:20' },
                { name: 'Projets', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-14 09:15' },
                { name: 'Windows', type: 'directory', icon: '📁', size: '<DIR>', modified: '2024-12-01 16:45' }
            ],
            'C:\\Projets\\FRED2000\\': [
                { name: '.. (Dossier parent)', type: 'parent', icon: '⬆️' },
                { name: 'fvbco020.ec', type: 'file', icon: '🔧', size: '2.5 MB', modified: '2025-01-14 08:30', is_ec: true },
                { name: 'fvbco021.ec', type: 'file', icon: '🔧', size: '1.8 MB', modified: '2025-01-13 16:45', is_ec: true },
                { name: 'fvbco022.ec', type: 'file', icon: '🔧', size: '3.2 MB', modified: '2025-01-12 11:20', is_ec: true },
                { name: 'includes', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-10 14:30' },
                { name: 'docs', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-08 09:15' },
                { name: 'backup', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-05 17:00' }
            ],
            'home': [
                { name: 'Documents', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-14 10:00' },
                { name: 'Desktop', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-14 09:30' },
                { name: 'Downloads', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-14 08:45' },
                { name: 'fred_project.ec', type: 'file', icon: '🔧', size: '1.2 MB', modified: '2025-01-13 15:20', is_ec: true }
            ],
            'desktop': [
                { name: 'projet_fred.ec', type: 'file', icon: '🔧', size: '890 KB', modified: '2025-01-14 07:15', is_ec: true },
                { name: 'notes.txt', type: 'file', icon: '📄', size: '5 KB', modified: '2025-01-14 08:00' },
                { name: 'Raccourcis', type: 'directory', icon: '📁', size: '<DIR>', modified: '2025-01-10 12:00' }
            ]
        };

        // Initialisation
        window.onload = function() {
            loadDrive('C:\\Projets\\FRED2000\\');
        };

        function loadDrive(location) {
            currentLocation = location;
            document.getElementById('currentPath').textContent = location;
            
            const items = demoData[location] || [];
            displayFiles(items);
        }

        function displayFiles(items) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            items.forEach(item => {
                const fileElement = document.createElement('div');
                fileElement.className = 'file-item';
                
                if (item.is_ec) {
                    fileElement.classList.add('ec-file');
                    const filePath = currentLocation + item.name;
                    
                    if (selectedFiles.has(filePath)) {
                        fileElement.classList.add('selected');
                    }
                    
                    fileElement.onclick = () => toggleFileSelection(filePath, item.name, fileElement);
                } else if (item.type === 'directory') {
                    fileElement.onclick = () => {
                        if (item.name === 'Projets') {
                            loadDrive('C:\\Projets\\FRED2000\\');
                        } else {
                            showStatus('info', `Navigation vers ${item.name} (démo)`);
                        }
                    };
                } else if (item.type === 'parent') {
                    fileElement.onclick = () => {
                        if (currentLocation === 'C:\\Projets\\FRED2000\\') {
                            loadDrive('C:\\');
                        } else {
                            showStatus('info', 'Navigation vers le parent (démo)');
                        }
                    };
                }
                
                let checkbox = '';
                if (item.is_ec) {
                    const filePath = currentLocation + item.name;
                    const isSelected = selectedFiles.has(filePath);
                    checkbox = `<input type="checkbox" class="file-checkbox" ${isSelected ? 'checked' : ''} onclick="event.stopPropagation(); toggleFileSelection('${filePath}', '${item.name}', this.parentElement)">`;
                }
                
                fileElement.innerHTML = `
                    <div class="file-icon">${item.icon}</div>
                    <div class="file-info">
                        <div class="file-name">${item.name}</div>
                        <div class="file-details">${item.size || ''} ${item.modified || ''}</div>
                    </div>
                    ${checkbox}
                `;
                
                fileList.appendChild(fileElement);
            });
        }

        function toggleFileSelection(filePath, fileName, element) {
            if (selectedFiles.has(filePath)) {
                selectedFiles.delete(filePath);
                element.classList.remove('selected');
                const checkbox = element.querySelector('.file-checkbox');
                if (checkbox) checkbox.checked = false;
            } else {
                selectedFiles.add(filePath);
                element.classList.add('selected');
                const checkbox = element.querySelector('.file-checkbox');
                if (checkbox) checkbox.checked = true;
            }
            
            updateSelectedFilesDisplay();
        }

        function updateSelectedFilesDisplay() {
            const selectedFilesDiv = document.getElementById('selectedFiles');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            if (selectedFiles.size === 0) {
                selectedFilesDiv.innerHTML = '<p style="color: #6c757d; font-style: italic;">Aucun fichier .ec sélectionné</p>';
                analyzeBtn.disabled = true;
            } else {
                let html = `<h4>${selectedFiles.size} fichier(s) sélectionné(s):</h4>`;
                selectedFiles.forEach(filePath => {
                    const fileName = filePath.split(/[\\\\/]/).pop();
                    html += `
                        <div class="selected-file">
                            <span>🔧 ${fileName}</span>
                            <button class="remove-file" onclick="removeFileFromSelection('${filePath}')">✕</button>
                        </div>
                    `;
                });
                selectedFilesDiv.innerHTML = html;
                analyzeBtn.disabled = false;
            }
        }

        function removeFileFromSelection(filePath) {
            selectedFiles.delete(filePath);
            updateSelectedFilesDisplay();
            
            // Mettre à jour l'affichage dans la liste des fichiers
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const checkbox = item.querySelector('.file-checkbox');
                if (checkbox && checkbox.onclick.toString().includes(filePath.replace(/\\/g, '\\\\'))) {
                    item.classList.remove('selected');
                    checkbox.checked = false;
                }
            });
        }

        function clearSelection() {
            selectedFiles.clear();
            updateSelectedFilesDisplay();
            
            document.querySelectorAll('.file-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.parentElement.classList.remove('selected');
            });
        }

        function startDemoAnalysis() {
            if (selectedFiles.size === 0) {
                showStatus('error', 'Aucun fichier sélectionné');
                return;
            }
            
            showStatus('info', 'Démarrage de l\'analyse de démonstration...');
            
            // Simulation d'analyse
            setTimeout(() => {
                const demoResults = generateDemoResults();
                document.getElementById('resultsContent').textContent = demoResults;
                showStatus('success', 'Analyse de démonstration terminée!');
            }, 2000);
        }

        function generateDemoResults() {
            const fileCount = selectedFiles.size;
            const totalIssues = Math.floor(Math.random() * 20) + 5;
            
            let results = `RÉSULTATS D'ANALYSE FRED2000 (DÉMONSTRATION)
${'='.repeat(50)}

📅 Date d'analyse: ${new Date().toLocaleString()}
📄 Fichiers analysés: ${fileCount}
🚨 Total problèmes détectés: ${totalIssues}

📊 RÉPARTITION PAR GRAVITÉ:
   🔴 Critique: ${Math.floor(totalIssues * 0.1)}
   🟠 Élevé: ${Math.floor(totalIssues * 0.3)}
   🟡 Moyen: ${Math.floor(totalIssues * 0.4)}
   🔵 Faible: ${Math.floor(totalIssues * 0.2)}

📋 TYPES DE PROBLÈMES DÉTECTÉS:
   • Erreurs SQL: ${Math.floor(totalIssues * 0.4)}
   • Problèmes mémoire: ${Math.floor(totalIssues * 0.3)}
   • Logique métier: ${Math.floor(totalIssues * 0.2)}
   • Performance: ${Math.floor(totalIssues * 0.1)}

📄 DÉTAILS PAR FICHIER:
`;

            selectedFiles.forEach(filePath => {
                const fileName = filePath.split(/[\\\\/]/).pop();
                const fileIssues = Math.floor(Math.random() * 8) + 1;
                
                results += `
📄 ${fileName}
   Lignes: ${Math.floor(Math.random() * 5000) + 1000}
   Fonctions: ${Math.floor(Math.random() * 50) + 10}
   Problèmes: ${fileIssues}

   🔍 Exemples de problèmes:
   1. [ÉLEVÉ] Ligne 245 - Utilisation de strcpy() sans vérification
   2. [MOYEN] Ligne 389 - Requête SQL sans gestion d'erreur
   3. [FAIBLE] Ligne 156 - Variable non utilisée
`;
            });

            results += `
💡 RECOMMANDATIONS:
🟡 État correct. Recommandé de corriger les problèmes de haute priorité.
🗄️ Vérifiez la gestion d'erreurs SQL et les transactions.
🛡️ Attention aux fonctions dangereuses (strcpy, sprintf, etc.).

📄 RAPPORTS GÉNÉRÉS:
   - Rapport HTML complet avec graphiques
   - Données JSON pour intégration
   - Liste CSV pour Excel

🚀 Pour une analyse réelle, installez Python et utilisez l'application complète!`;

            return results;
        }

        function refreshDemo() {
            showStatus('info', 'Actualisation de la démonstration...');
            setTimeout(() => {
                loadDrive(currentLocation);
                showStatus('success', 'Démonstration actualisée!');
            }, 500);
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('analysisStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.classList.remove('hidden');
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusDiv.classList.add('hidden');
                }, 3000);
            }
        }
    </script>
</body>
</html>
