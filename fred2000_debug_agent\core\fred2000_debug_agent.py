#!/usr/bin/env python3
"""
Debug Agent - Moteur Principal
Agent de débogage spécialisé pour les fichiers .ec du système FRED2000

Auteur: Assistant IA
Date: 2025-01-14
"""

import os
import re
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Callable

class ErrorType(Enum):
    """Types d'erreurs détectées"""
    SQL_ERROR = "sql_error"
    BUFFER_OVERFLOW = "buffer_overflow" 
    MEMORY_LEAK = "memory_leak"
    NULL_POINTER = "null_pointer"
    LOGIC_ERROR = "logic_error"
    PERFORMANCE = "performance"
    SYNTAX_ERROR = "syntax_error"
    UNDEFINED_VARIABLE = "undefined_variable"

class Severity(Enum):
    """Niveaux de gravité"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class CodeIssue:
    """Représente un problème détecté dans le code"""
    error_type: ErrorType
    severity: Severity
    line_number: int
    function_name: str
    description: str
    code_snippet: str
    suggestion: str

class FRED2000DebugAgent:
    """Agent principal de débogage pour FRED2000"""
    
    def __init__(self, file_path: str):
        """
        Initialise l'agent avec un fichier .ec
        
        Args:
            file_path: Chemin vers le fichier .ec à analyser
        """
        self.file_path = file_path
        self.code_lines = []
        self.functions = {}
        self.variables = set()
        self.issues = []
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Fichier non trouvé: {file_path}")
        
        self._load_file()
        self._parse_structure()
    
    def _load_file(self):
        """Charge le contenu du fichier"""
        try:
            with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                self.code_lines = f.readlines()
        except UnicodeDecodeError:
            # Essayer avec d'autres encodages
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(self.file_path, 'r', encoding=encoding) as f:
                        self.code_lines = f.readlines()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError(f"Impossible de décoder le fichier: {self.file_path}")
    
    def _parse_structure(self):
        """Parse la structure du code (fonctions, variables)"""
        current_function = None
        brace_count = 0
        
        for i, line in enumerate(self.code_lines, 1):
            line_clean = line.strip()
            
            # Détecter les fonctions
            func_match = re.match(r'^\s*(?:static\s+)?(?:int|void|char\*?|long|short|double|float)\s+(\w+)\s*\([^)]*\)\s*{?', line)
            if func_match:
                func_name = func_match.group(1)
                self.functions[func_name] = {
                    'start_line': i,
                    'end_line': None,
                    'variables': set()
                }
                current_function = func_name
                brace_count = line.count('{') - line.count('}')
            
            # Suivre les accolades pour déterminer la fin des fonctions
            if current_function:
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0 and '}' in line:
                    self.functions[current_function]['end_line'] = i
                    current_function = None
                    brace_count = 0
            
            # Détecter les variables locales
            var_matches = re.findall(r'\b(?:int|char|long|short|double|float|void)\s+(\w+)', line)
            for var in var_matches:
                self.variables.add(var)
                if current_function:
                    self.functions[current_function]['variables'].add(var)
    
    def get_function_at_line(self, line_number: int) -> Optional[str]:
        """Retourne le nom de la fonction à une ligne donnée"""
        for func_name, func_info in self.functions.items():
            start = func_info['start_line']
            end = func_info.get('end_line', len(self.code_lines))
            if start <= line_number <= end:
                return func_name
        return None
    
    def run_full_analysis(self) -> List[CodeIssue]:
        """Lance une analyse complète du fichier"""
        self.issues = []
        
        # Import des détecteurs (import local pour éviter les dépendances circulaires)
        try:
            from .error_detectors import (
                SQLErrorDetector, MemoryErrorDetector, 
                BusinessLogicDetector, PerformanceDetector
            )
            from .static_analyzer import FRED2000StaticAnalyzer
            
            # Lancer tous les détecteurs
            detectors = [
                SQLErrorDetector(),
                MemoryErrorDetector(),
                BusinessLogicDetector(),
                PerformanceDetector()
            ]
            
            for detector in detectors:
                issues = detector.detect_issues(self.code_lines, self.get_function_at_line)
                self.issues.extend(issues)
            
            # Analyse statique
            static_analyzer = FRED2000StaticAnalyzer()
            static_issues = static_analyzer.analyze_code(self.code_lines)
            self.issues.extend(static_issues)
            
        except ImportError as e:
            print(f"Avertissement: Impossible d'importer tous les détecteurs: {e}")
        
        return self.issues
    
    def get_statistics(self) -> Dict:
        """Retourne des statistiques sur le fichier"""
        return {
            'total_lines': len(self.code_lines),
            'total_functions': len(self.functions),
            'total_variables': len(self.variables),
            'total_issues': len(self.issues),
            'file_size': os.path.getsize(self.file_path)
        }
    
    def get_issues_by_severity(self) -> Dict[str, List[CodeIssue]]:
        """Groupe les problèmes par gravité"""
        by_severity = {
            'critical': [],
            'high': [],
            'medium': [],
            'low': []
        }
        
        for issue in self.issues:
            by_severity[issue.severity.value].append(issue)
        
        return by_severity
    
    def get_issues_by_type(self) -> Dict[str, List[CodeIssue]]:
        """Groupe les problèmes par type"""
        by_type = {}
        
        for issue in self.issues:
            issue_type = issue.error_type.value
            if issue_type not in by_type:
                by_type[issue_type] = []
            by_type[issue_type].append(issue)
        
        return by_type

def main():
    """Fonction principale pour test"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python fred2000_debug_agent.py <fichier.ec>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    try:
        agent = FRED2000DebugAgent(file_path)
        print(f"📄 Analyse de: {file_path}")
        print(f"📊 Statistiques: {agent.get_statistics()}")
        
        issues = agent.run_full_analysis()
        print(f"🔍 Problèmes détectés: {len(issues)}")
        
        for issue in issues[:5]:  # Afficher les 5 premiers
            print(f"  - Ligne {issue.line_number}: {issue.description}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
