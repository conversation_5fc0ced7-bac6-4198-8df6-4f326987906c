@echo off
echo ============================================================
echo    Debug Agent - Explorateur de Fichiers Web
echo    Naviguez dans votre PC pour analyser vos fichiers .ec
echo    Version 1.0 - 2025-01-14
echo ============================================================
echo.

REM Changer vers le repertoire du script
cd /d "%~dp0"

echo Verification de Python...

REM Essayer python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python trouve!
    echo Lancement de l'explorateur de fichiers web...
    echo.
    echo ============================================================
    echo    Interface disponible sur: http://localhost:8080
    echo    Naviguez dans votre PC et selectionnez vos fichiers .ec
    echo ============================================================
    echo.
    python file_explorer_app.py
    goto :end
)

REM Essayer python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python3 trouve!
    echo Lancement de l'explorateur de fichiers web...
    echo.
    echo ============================================================
    echo    Interface disponible sur: http://localhost:8080
    echo    Naviguez dans votre PC et selectionnez vos fichiers .ec
    echo ============================================================
    echo.
    python3 file_explorer_app.py
    goto :end
)

REM Essayer py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python (py) trouve!
    echo Lancement de l'explorateur de fichiers web...
    echo.
    echo ============================================================
    echo    Interface disponible sur: http://localhost:8080
    echo    Naviguez dans votre PC et selectionnez vos fichiers .ec
    echo ============================================================
    echo.
    py file_explorer_app.py
    goto :end
)

REM Python non trouve
echo.
echo ERREUR: Python n'est pas installe ou non accessible.
echo.
echo Solutions:
echo 1. Installez Python depuis https://python.org
echo 2. Ou installez depuis le Microsoft Store
echo 3. Verifiez que Python est dans le PATH
echo.
echo Appuyez sur une touche pour ouvrir le site Python...
pause >nul
start https://python.org/downloads/
goto :end

:end
echo.
echo Application fermee.
echo Appuyez sur une touche pour fermer...
pause >nul
