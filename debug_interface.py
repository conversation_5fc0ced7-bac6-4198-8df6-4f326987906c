#!/usr/bin/env python3
"""
Interface utilisateur pour l'agent de débogage FRED2000
Interface en ligne de commande interactive

Auteur: Assistant IA
Date: 2025-01-14
"""

import os
import sys
import json
from typing import List, Dict
import argparse
from datetime import datetime

# Import des modules de l'agent
from fred2000_debug_agent import FRED2000DebugAgent, ErrorType, Severity
from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
from static_analyzer import FRED2000StaticAnalyzer

class FRED2000DebugInterface:
    """Interface utilisateur pour l'agent de débogage"""
    
    def __init__(self):
        self.agent = None
        self.source_file = "fvbco020.ec"
        self.current_issues = []
        
        # Détecteurs spécialisés
        self.sql_detector = SQLErrorDetector()
        self.memory_detector = MemoryErrorDetector()
        self.business_detector = BusinessLogicDetector()
        self.performance_detector = PerformanceDetector()
        self.static_analyzer = FRED2000StaticAnalyzer()
    
    def print_banner(self):
        """Affiche la bannière de l'application"""
        print("=" * 70)
        print(" DEBUG AGENT - Agent de Débogage Spécialisé")
        print("   Système de facturation FRED2000 (fvbco020.ec)")
        print("   Version 1.0 - 2025-01-14")
        print("=" * 70)
        print()
    
    def print_menu(self):
        """Affiche le menu principal"""
        print("📋 MENU PRINCIPAL:")
        print("1. 🔍 Analyse complète du code")
        print("2. 🗄️  Analyse SQL/ESQL spécialisée")
        print("3. 🧠 Analyse mémoire et sécurité")
        print("4. 📊 Analyse logique métier FRED2000")
        print("5. ⚡ Analyse performance")
        print("6. 🔬 Analyse statique avancée")
        print("7. 📄 Générer rapport détaillé")
        print("8. 🔧 Configurer fichier source")
        print("9. 📈 Statistiques du code")
        print("0. ❌ Quitter")
        print("-" * 50)
    
    def initialize_agent(self):
        """Initialise l'agent de débogage"""
        try:
            print(f"🚀 Initialisation de l'agent pour: {self.source_file}")
            self.agent = FRED2000DebugAgent(self.source_file)
            print("✅ Agent initialisé avec succès!")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de l'initialisation: {e}")
            return False
    
    def run_complete_analysis(self):
        """Lance une analyse complète"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("🔍 Lancement de l'analyse complète...")
        print("=" * 50)
        
        # Analyse principale
        issues = self.agent.run_full_analysis()
        
        # Analyses spécialisées
        print("🔍 Analyse SQL spécialisée...")
        sql_issues = self.sql_detector.detect_issues(
            self.agent.code_lines, 
            self.agent.get_function_at_line
        )
        
        print("🔍 Analyse mémoire spécialisée...")
        memory_issues = self.memory_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        print("🔍 Analyse logique métier...")
        business_issues = self.business_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        # Combiner tous les problèmes
        all_issues = issues + sql_issues + memory_issues + business_issues
        self.current_issues = all_issues
        
        # Afficher résumé
        self.display_issues_summary(all_issues)
    
    def run_sql_analysis(self):
        """Lance l'analyse SQL spécialisée"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("🗄️ Analyse SQL/ESQL spécialisée...")
        issues = self.sql_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        self.display_issues_detail(issues, "SQL/ESQL")
    
    def run_memory_analysis(self):
        """Lance l'analyse mémoire"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("🧠 Analyse mémoire et sécurité...")
        issues = self.memory_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        self.display_issues_detail(issues, "Mémoire/Sécurité")
    
    def run_business_analysis(self):
        """Lance l'analyse logique métier"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("📊 Analyse logique métier FRED2000...")
        issues = self.business_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        self.display_issues_detail(issues, "Logique Métier")
    
    def run_performance_analysis(self):
        """Lance l'analyse performance"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("⚡ Analyse performance...")
        issues = self.performance_detector.detect_issues(
            self.agent.code_lines,
            self.agent.get_function_at_line
        )
        
        self.display_issues_detail(issues, "Performance")
    
    def run_static_analysis(self):
        """Lance l'analyse statique avancée"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("🔬 Analyse statique avancée...")
        issues = self.static_analyzer.analyze_code(self.agent.code_lines)
        
        # Rapport de complexité
        complexity = self.static_analyzer.generate_complexity_report()
        
        print(f"\n📊 RAPPORT DE COMPLEXITÉ:")
        print(f"   Fonctions totales: {complexity['total_functions']}")
        print(f"   Lignes totales: {complexity['total_lines']}")
        print(f"   Longueur moyenne: {complexity['average_function_length']:.1f} lignes")
        print(f"   Fonctions complexes: {len(complexity['complex_functions'])}")
        
        if complexity['complex_functions']:
            print("\n🚨 Fonctions complexes (>100 lignes):")
            for func in complexity['complex_functions'][:5]:
                print(f"   - {func['name']}: {func['length']} lignes")
        
        self.display_issues_detail(issues, "Analyse Statique")
    
    def display_issues_summary(self, issues: List):
        """Affiche un résumé des problèmes"""
        if not issues:
            print("✅ Aucun problème détecté!")
            return
        
        # Compter par gravité
        severity_count = {}
        type_count = {}
        
        for issue in issues:
            severity = issue.severity.value
            error_type = issue.error_type.value
            
            severity_count[severity] = severity_count.get(severity, 0) + 1
            type_count[error_type] = type_count.get(error_type, 0) + 1
        
        print(f"\n📊 RÉSUMÉ: {len(issues)} problèmes détectés")
        print("=" * 40)
        
        print("Par gravité:")
        for severity, count in sorted(severity_count.items()):
            emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🔵"}.get(severity, "⚪")
            print(f"   {emoji} {severity.upper()}: {count}")
        
        print("\nPar type:")
        for error_type, count in sorted(type_count.items()):
            print(f"   • {error_type.replace('_', ' ').title()}: {count}")
        
        print("\n🔍 Top 5 problèmes:")
        for i, issue in enumerate(issues[:5], 1):
            print(f"   {i}. Ligne {issue.line_number} - {issue.description}")
    
    def display_issues_detail(self, issues: List, analysis_type: str):
        """Affiche les détails des problèmes"""
        if not issues:
            print(f"✅ Aucun problème {analysis_type} détecté!")
            return
        
        print(f"\n🔍 PROBLÈMES {analysis_type.upper()}: {len(issues)} détectés")
        print("=" * 60)
        
        for i, issue in enumerate(issues[:10], 1):  # Limiter à 10
            severity_emoji = {
                "critical": "🔴", "high": "🟠", 
                "medium": "🟡", "low": "🔵"
            }.get(issue.severity.value, "⚪")
            
            print(f"{i}. {severity_emoji} Ligne {issue.line_number} - {issue.function_name}()")
            print(f"   Type: {issue.error_type.value.replace('_', ' ').title()}")
            print(f"   Description: {issue.description}")
            print(f"   Code: {issue.code_snippet}")
            print(f"   💡 Suggestion: {issue.suggestion}")
            print("-" * 40)
        
        if len(issues) > 10:
            print(f"... et {len(issues) - 10} autres problèmes")
    
    def generate_report(self):
        """Génère un rapport détaillé"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"fred2000_debug_report_{timestamp}.json"
        
        print(f"📄 Génération du rapport: {report_file}")
        
        # Utiliser les problèmes actuels ou faire une analyse rapide
        if not self.current_issues:
            print("🔍 Analyse rapide pour le rapport...")
            self.current_issues = self.agent.run_full_analysis()
        
        report_path = self.agent.generate_report(report_file)
        print(f"✅ Rapport généré: {report_path}")
    
    def configure_source_file(self):
        """Configure le fichier source"""
        print("🔧 Configuration du fichier source")
        print(f"Fichier actuel: {self.source_file}")
        
        new_file = input("Nouveau fichier (ou Entrée pour garder actuel): ").strip()
        if new_file and os.path.exists(new_file):
            self.source_file = new_file
            self.agent = None  # Réinitialiser l'agent
            print(f"✅ Fichier configuré: {self.source_file}")
        elif new_file:
            print(f"❌ Fichier non trouvé: {new_file}")
    
    def show_statistics(self):
        """Affiche les statistiques du code"""
        if not self.agent:
            print("❌ Agent non initialisé!")
            return
        
        print("📈 STATISTIQUES DU CODE")
        print("=" * 40)
        print(f"Fichier: {self.source_file}")
        print(f"Lignes totales: {len(self.agent.code_lines)}")
        print(f"Fonctions: {len(self.agent.functions)}")
        
        if self.agent.functions:
            func_lengths = [
                end - start for start, end in self.agent.functions.values()
                if end > 0
            ]
            if func_lengths:
                print(f"Longueur moyenne des fonctions: {sum(func_lengths) / len(func_lengths):.1f} lignes")
                print(f"Plus grande fonction: {max(func_lengths)} lignes")
        
        # Compter les patterns
        sql_count = sum(1 for line in self.agent.code_lines if 'EXEC SQL' in line)
        print(f"Requêtes SQL: {sql_count}")
        
        malloc_count = sum(1 for line in self.agent.code_lines if 'malloc' in line or 'calloc' in line)
        print(f"Allocations mémoire: {malloc_count}")
    
    def run(self):
        """Lance l'interface principale"""
        self.print_banner()
        
        # Initialiser l'agent
        if not self.initialize_agent():
            return
        
        while True:
            print()
            self.print_menu()
            
            try:
                choice = input("Votre choix: ").strip()
                
                if choice == "0":
                    print("👋 Au revoir!")
                    break
                elif choice == "1":
                    self.run_complete_analysis()
                elif choice == "2":
                    self.run_sql_analysis()
                elif choice == "3":
                    self.run_memory_analysis()
                elif choice == "4":
                    self.run_business_analysis()
                elif choice == "5":
                    self.run_performance_analysis()
                elif choice == "6":
                    self.run_static_analysis()
                elif choice == "7":
                    self.generate_report()
                elif choice == "8":
                    self.configure_source_file()
                elif choice == "9":
                    self.show_statistics()
                else:
                    print("❌ Choix invalide!")
                
                input("\nAppuyez sur Entrée pour continuer...")
                
            except KeyboardInterrupt:
                print("\n👋 Au revoir!")
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")

def main():
    """Point d'entrée principal"""
    parser = argparse.ArgumentParser(description="Agent de débogage FRED2000")
    parser.add_argument("--file", "-f", default="fvbco020.ec", 
                       help="Fichier source à analyser")
    parser.add_argument("--auto", "-a", action="store_true",
                       help="Analyse automatique sans interface")
    
    args = parser.parse_args()
    
    if args.auto:
        # Mode automatique
        agent = FRED2000DebugAgent(args.file)
        issues = agent.run_full_analysis()
        agent.generate_report()
        print(f"✅ Analyse terminée: {len(issues)} problèmes détectés")
    else:
        # Mode interactif
        interface = FRED2000DebugInterface()
        interface.source_file = args.file
        interface.run()

if __name__ == "__main__":
    main()
