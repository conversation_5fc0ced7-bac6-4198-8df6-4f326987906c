#!/usr/bin/env python3
"""
Debug Agent - Application Web avec Explorateur de Fichiers
Interface web permettant de naviguer dans tout le PC pour sélectionner les fichiers .ec

Auteur: Assistant IA
Date: 2025-01-14
"""

import http.server
import socketserver
import json
import os
import urllib.parse
import tempfile
import shutil
from datetime import datetime
import threading
import webbrowser
import time
import stat
from pathlib import Path

# Import des modules d'analyse
try:
    from fred2000_debug_agent import FRED2000DebugAgent
    from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
    from report_generator import FRED2000ReportGenerator
    ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modules d'analyse non disponibles: {e}")
    ANALYSIS_AVAILABLE = False

class FileExplorerHandler(http.server.SimpleHTTPRequestHandler):
    """Handler HTTP avec explorateur de fichiers intégré"""
    
    def __init__(self, *args, **kwargs):
        # Créer les dossiers nécessaires
        os.makedirs('temp_analysis', exist_ok=True)
        os.makedirs('reports', exist_ok=True)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Gestion des requêtes GET"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path.startswith('/api/browse'):
            self.handle_browse_directory()
        elif self.path.startswith('/api/drives'):
            self.handle_get_drives()
        elif self.path.startswith('/api/'):
            self.handle_api_get()
        else:
            super().do_GET()
    
    def do_POST(self):
        """Gestion des requêtes POST"""
        if self.path.startswith('/api/'):
            self.handle_api_post()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale avec explorateur de fichiers"""
        html_content = self.get_file_explorer_html()
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-length', len(html_content.encode('utf-8')))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def handle_get_drives(self):
        """Retourne la liste des lecteurs disponibles"""
        try:
            drives = []
            
            # Windows - Détecter les lecteurs
            if os.name == 'nt':
                import string
                for letter in string.ascii_uppercase:
                    drive_path = f"{letter}:\\"
                    if os.path.exists(drive_path):
                        try:
                            # Obtenir des infos sur le lecteur
                            stat_info = os.statvfs(drive_path) if hasattr(os, 'statvfs') else None
                            drive_info = {
                                'name': f"Lecteur {letter}:",
                                'path': drive_path,
                                'type': 'drive',
                                'size': self.get_drive_size(drive_path)
                            }
                            drives.append(drive_info)
                        except:
                            # Lecteur non accessible
                            drives.append({
                                'name': f"Lecteur {letter}: (non accessible)",
                                'path': drive_path,
                                'type': 'drive',
                                'size': 'N/A'
                            })
            else:
                # Linux/Mac - Commencer par la racine
                drives.append({
                    'name': 'Système de fichiers',
                    'path': '/',
                    'type': 'drive',
                    'size': 'N/A'
                })
            
            # Ajouter des raccourcis utiles
            home_dir = os.path.expanduser('~')
            if os.path.exists(home_dir):
                drives.append({
                    'name': '🏠 Dossier Personnel',
                    'path': home_dir,
                    'type': 'shortcut',
                    'size': 'N/A'
                })
            
            desktop_dir = os.path.join(home_dir, 'Desktop')
            if os.path.exists(desktop_dir):
                drives.append({
                    'name': '🖥️ Bureau',
                    'path': desktop_dir,
                    'type': 'shortcut',
                    'size': 'N/A'
                })
            
            documents_dir = os.path.join(home_dir, 'Documents')
            if os.path.exists(documents_dir):
                drives.append({
                    'name': '📁 Documents',
                    'path': documents_dir,
                    'type': 'shortcut',
                    'size': 'N/A'
                })
            
            self.send_json_response({'drives': drives})
            
        except Exception as e:
            self.send_json_response({'error': f'Erreur lors de la récupération des lecteurs: {str(e)}'}, 500)
    
    def handle_browse_directory(self):
        """Parcourt un répertoire et retourne son contenu"""
        try:
            # Extraire le chemin depuis l'URL
            query = urllib.parse.urlparse(self.path).query
            params = urllib.parse.parse_qs(query)
            
            directory_path = params.get('path', [''])[0]
            if not directory_path:
                self.send_json_response({'error': 'Chemin manquant'}, 400)
                return
            
            # Décoder le chemin
            directory_path = urllib.parse.unquote(directory_path)
            
            if not os.path.exists(directory_path):
                self.send_json_response({'error': 'Répertoire non trouvé'}, 404)
                return
            
            if not os.path.isdir(directory_path):
                self.send_json_response({'error': 'Le chemin n\'est pas un répertoire'}, 400)
                return
            
            # Lister le contenu du répertoire
            items = []
            ec_files_count = 0
            
            try:
                # Ajouter le dossier parent si ce n'est pas la racine
                parent_path = os.path.dirname(directory_path)
                if parent_path != directory_path:  # Pas la racine
                    items.append({
                        'name': '.. (Dossier parent)',
                        'path': parent_path,
                        'type': 'parent',
                        'size': '',
                        'modified': '',
                        'is_ec': False
                    })
                
                # Lister tous les éléments
                for item_name in sorted(os.listdir(directory_path)):
                    item_path = os.path.join(directory_path, item_name)
                    
                    try:
                        stat_info = os.stat(item_path)
                        is_dir = os.path.isdir(item_path)
                        is_ec = item_name.lower().endswith('.ec')
                        
                        if is_ec:
                            ec_files_count += 1
                        
                        # Formatage de la taille
                        if is_dir:
                            size_str = '<DIR>'
                        else:
                            size_str = self.format_file_size(stat_info.st_size)
                        
                        # Date de modification
                        modified_str = datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M')
                        
                        # Icône selon le type
                        if is_dir:
                            icon = '📁'
                        elif is_ec:
                            icon = '🔧'
                        else:
                            icon = '📄'
                        
                        items.append({
                            'name': item_name,
                            'path': item_path,
                            'type': 'directory' if is_dir else 'file',
                            'size': size_str,
                            'modified': modified_str,
                            'is_ec': is_ec,
                            'icon': icon
                        })
                        
                    except (OSError, PermissionError):
                        # Fichier/dossier non accessible
                        items.append({
                            'name': item_name + ' (non accessible)',
                            'path': item_path,
                            'type': 'inaccessible',
                            'size': 'N/A',
                            'modified': 'N/A',
                            'is_ec': False,
                            'icon': '🚫'
                        })
                
                response = {
                    'current_path': directory_path,
                    'items': items,
                    'ec_files_count': ec_files_count,
                    'total_items': len(items) - (1 if parent_path != directory_path else 0)  # Exclure le parent du compte
                }
                
                self.send_json_response(response)
                
            except PermissionError:
                self.send_json_response({'error': 'Accès refusé à ce répertoire'}, 403)
            except Exception as e:
                self.send_json_response({'error': f'Erreur lors de la lecture du répertoire: {str(e)}'}, 500)
                
        except Exception as e:
            self.send_json_response({'error': f'Erreur lors du parcours: {str(e)}'}, 500)
    
    def get_drive_size(self, drive_path):
        """Obtient la taille d'un lecteur"""
        try:
            if hasattr(shutil, 'disk_usage'):
                total, used, free = shutil.disk_usage(drive_path)
                return self.format_file_size(total)
            else:
                return 'N/A'
        except:
            return 'N/A'
    
    def format_file_size(self, size_bytes):
        """Formate la taille d'un fichier"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def handle_api_get(self):
        """Gestion des autres API GET"""
        if self.path == '/api/health':
            self.send_json_response({
                'status': 'healthy',
                'analysis_available': ANALYSIS_AVAILABLE,
                'timestamp': datetime.now().isoformat()
            })
        elif self.path.startswith('/api/results/'):
            self.handle_get_results()
        else:
            self.send_error(404)

    def handle_get_results(self):
        """Récupère les résultats d'analyse"""
        try:
            # Extraire l'ID d'analyse depuis l'URL
            analysis_id = self.path.split('/')[-1]

            # Chercher le fichier de résultats
            results_file = f'reports/analysis_results_{analysis_id}.json'

            if os.path.exists(results_file):
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)

                # Calculer les statistiques
                stats = self.calculate_statistics(results)

                self.send_json_response({
                    'success': True,
                    'results': results,
                    'statistics': stats,
                    'analysis_id': analysis_id
                })
            else:
                self.send_json_response({'error': 'Résultats non trouvés'}, 404)

        except Exception as e:
            self.send_json_response({'error': f'Erreur lors de la récupération: {str(e)}'}, 500)

    def calculate_statistics(self, results):
        """Calcule les statistiques des résultats"""
        stats = {
            'total_files': len(results),
            'total_issues': 0,
            'severity_counts': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
            'type_counts': {},
            'files_with_issues': 0
        }

        for result in results.values():
            issues = result.get('issues', [])
            if issues:
                stats['files_with_issues'] += 1

            stats['total_issues'] += len(issues)

            for issue in issues:
                severity = issue.get('severity', 'unknown')
                issue_type = issue.get('type', 'unknown')

                if severity in stats['severity_counts']:
                    stats['severity_counts'][severity] += 1

                stats['type_counts'][issue_type] = stats['type_counts'].get(issue_type, 0) + 1

        return stats
    
    def handle_api_post(self):
        """Gestion des API POST"""
        if self.path == '/api/analyze':
            self.handle_analyze()
        elif self.path == '/api/select-files':
            self.handle_file_selection()
        else:
            self.send_error(404)

    def handle_file_selection(self):
        """Gestion de la sélection de fichiers via dialogue natif"""
        try:
            # Cette API sera appelée par le frontend qui utilisera l'input file HTML5
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            selected_files = data.get('files', [])

            # Valider que les fichiers existent et sont des .ec
            valid_files = []
            for file_info in selected_files:
                file_path = file_info.get('path', '')
                file_name = file_info.get('name', '')

                if file_name.lower().endswith('.ec'):
                    valid_files.append({
                        'name': file_name,
                        'path': file_path,
                        'size': file_info.get('size', 0),
                        'type': 'ec'
                    })

            self.send_json_response({
                'success': True,
                'files': valid_files,
                'message': f'{len(valid_files)} fichier(s) .ec sélectionné(s)'
            })

        except Exception as e:
            self.send_json_response({'error': f'Erreur sélection: {str(e)}'}, 500)
    
    def handle_analyze(self):
        """Gestion de l'analyse des fichiers sélectionnés"""
        try:
            if not ANALYSIS_AVAILABLE:
                self.send_json_response({
                    'error': 'Modules d\'analyse non disponibles. Vérifiez l\'installation.'
                }, 500)
                return
            
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            selected_files = data.get('files', [])
            options = data.get('options', {})
            
            if not selected_files:
                self.send_json_response({'error': 'Aucun fichier sélectionné'}, 400)
                return
            
            # Vérifier que tous les fichiers existent
            valid_files = []
            for file_path in selected_files:
                if os.path.exists(file_path) and file_path.lower().endswith('.ec'):
                    valid_files.append(file_path)
            
            if not valid_files:
                self.send_json_response({'error': 'Aucun fichier .ec valide trouvé'}, 400)
                return
            
            # Lancer l'analyse dans un thread séparé
            analysis_id = f"analysis_{int(time.time())}"

            def run_analysis():
                try:
                    results = {}
                    total_files = len(valid_files)

                    print(f"🚀 Démarrage de l'analyse de {total_files} fichier(s)")

                    for i, file_path in enumerate(valid_files):
                        file_name = os.path.basename(file_path)
                        print(f"📄 Analyse de {file_name} ({i+1}/{total_files})...")

                        file_results = self.analyze_single_file(file_path, options)
                        results[file_name] = file_results

                        issues_count = len(file_results.get('issues', []))
                        print(f"✅ {file_name}: {issues_count} problème(s) détecté(s)")

                    # Sauvegarder les résultats
                    results_file = f'reports/analysis_results_{analysis_id}.json'
                    with open(results_file, 'w', encoding='utf-8') as f:
                        json.dump(results, f, indent=2, ensure_ascii=False)

                    # Générer un rapport HTML
                    if ANALYSIS_AVAILABLE:
                        try:
                            generator = FRED2000ReportGenerator()
                            html_report = generator.generate_html_report(results, f'reports/report_{analysis_id}.html')
                            print(f"📄 Rapport HTML généré: {html_report}")
                        except Exception as e:
                            print(f"⚠ Erreur génération rapport HTML: {e}")

                    total_issues = sum(len(result.get('issues', [])) for result in results.values())
                    print(f"🎉 Analyse terminée: {total_issues} problèmes détectés au total")
                    print(f"📊 Résultats disponibles via: /api/results/{analysis_id}")

                except Exception as e:
                    print(f"❌ Erreur lors de l'analyse: {e}")

            # Lancer l'analyse
            thread = threading.Thread(target=run_analysis)
            thread.daemon = True
            thread.start()

            self.send_json_response({
                'analysis_id': analysis_id,
                'message': f'Analyse démarrée pour {len(valid_files)} fichier(s)',
                'status': 'starting',
                'files_count': len(valid_files)
            })
            
        except Exception as e:
            self.send_json_response({'error': f'Erreur analyse: {str(e)}'}, 500)
    
    def analyze_single_file(self, file_path, options):
        """Analyse un seul fichier"""
        results = {
            'file_path': file_path,
            'issues': [],
            'stats': {},
            'analysis_types': []
        }
        
        try:
            # Créer l'agent pour ce fichier
            agent = FRED2000DebugAgent(file_path)
            
            # Statistiques de base
            results['stats'] = {
                'total_lines': len(agent.code_lines),
                'total_functions': len(agent.functions),
                'file_size': os.path.getsize(file_path)
            }
            
            all_issues = []
            
            # Analyses selon les options
            if options.get('sql_analysis', True):
                sql_detector = SQLErrorDetector()
                sql_issues = sql_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(sql_issues)
                results['analysis_types'].append('SQL')
            
            if options.get('memory_analysis', True):
                memory_detector = MemoryErrorDetector()
                memory_issues = memory_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(memory_issues)
                results['analysis_types'].append('Mémoire')
            
            if options.get('business_analysis', True):
                business_detector = BusinessLogicDetector()
                business_issues = business_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(business_issues)
                results['analysis_types'].append('Métier')
            
            if options.get('performance_analysis', True):
                performance_detector = PerformanceDetector()
                performance_issues = performance_detector.detect_issues(agent.code_lines, agent.get_function_at_line)
                all_issues.extend(performance_issues)
                results['analysis_types'].append('Performance')
            
            # Convertir les issues en dictionnaires
            results['issues'] = [
                {
                    'type': issue.error_type.value,
                    'severity': issue.severity.value,
                    'line': issue.line_number,
                    'function': issue.function_name,
                    'description': issue.description,
                    'code': issue.code_snippet,
                    'suggestion': issue.suggestion
                }
                for issue in all_issues
            ]
            
        except Exception as e:
            results['error'] = str(e)
        
        return results
    
    def send_json_response(self, data, status_code=200):
        """Envoie une réponse JSON"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-length', len(json_data.encode('utf-8')))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))

    def get_file_explorer_html(self):
        """Retourne le HTML avec explorateur de fichiers intégré"""
        return '''<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Agent - Explorateur de Fichiers</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .main-layout { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .explorer-card { height: 600px; display: flex; flex-direction: column; }
        .analysis-card { height: 600px; display: flex; flex-direction: column; }

        /* Explorateur de fichiers */
        .explorer-header { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .path-display { flex: 1; background: #f8f9fa; padding: 8px 12px; border-radius: 5px; font-family: monospace; font-size: 0.9em; }
        .drives-section { margin-bottom: 15px; }
        .drives-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin-bottom: 15px; }
        .drive-item { background: #f8f9fa; padding: 10px; border-radius: 8px; cursor: pointer; transition: all 0.3s; border: 2px solid transparent; }
        .drive-item:hover { background: #e9ecef; border-color: #667eea; }
        .drive-name { font-weight: 600; margin-bottom: 5px; }
        .drive-size { font-size: 0.8em; color: #6c757d; }

        .file-browser { flex: 1; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .file-list { height: 100%; overflow-y: auto; }
        .file-item { display: flex; align-items: center; padding: 8px 12px; border-bottom: 1px solid #f0f0f0; cursor: pointer; transition: all 0.3s; }
        .file-item:hover { background: #f8f9fa; }
        .file-item.selected { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .file-item.ec-file { background: #fff3e0; }
        .file-item.ec-file.selected { background: #ffcc80; }
        .file-icon { margin-right: 10px; font-size: 1.2em; }
        .file-info { flex: 1; }
        .file-name { font-weight: 500; }
        .file-details { font-size: 0.8em; color: #6c757d; }
        .file-checkbox { margin-left: auto; }

        /* Section d'analyse */
        .analysis-header { margin-bottom: 20px; }
        .selected-files { background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 20px; max-height: 200px; overflow-y: auto; }
        .selected-file { display: flex; justify-content: space-between; align-items: center; padding: 5px 0; }
        .remove-file { background: #dc3545; color: white; border: none; border-radius: 3px; padding: 2px 8px; cursor: pointer; }

        .analysis-options { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px; }
        .option { display: flex; align-items: center; gap: 8px; }

        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer; font-size: 16px; font-weight: 600; margin: 5px; transition: all 0.3s; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6c757d; }
        .btn-small { padding: 6px 12px; font-size: 14px; }

        .status { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .results-section { margin-top: 20px; }
        .results-content { background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em; white-space: pre-wrap; }

        .loading { text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 15px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .hidden { display: none !important; }

        @media (max-width: 1200px) {
            .main-layout { grid-template-columns: 1fr; }
            .explorer-card, .analysis-card { height: auto; min-height: 400px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Debug Agent</h1>
            <p>Explorateur de Fichiers - Naviguez dans votre PC pour analyser vos fichiers .ec</p>
        </div>

        <div class="main-layout">
            <!-- Explorateur de fichiers -->
            <div class="card explorer-card">
                <div class="explorer-header">
                    <h2>📁 Explorateur de Fichiers</h2>
                    <button class="btn btn-small" onclick="openNativeFileDialog()">📂 Parcourir...</button>
                    <button class="btn btn-small" onclick="refreshCurrentDirectory()">🔄 Actualiser</button>
                </div>

                <!-- Input file caché pour la sélection native -->
                <input type="file" id="nativeFileInput" multiple accept=".ec" style="display: none;" onchange="handleNativeFileSelection(event)">

                <div class="path-display" id="currentPath">Chargement...</div>

                <div class="drives-section">
                    <h3>💾 Lecteurs et Raccourcis</h3>
                    <div class="drives-grid" id="drivesGrid">
                        <div class="loading">
                            <div class="spinner"></div>
                            Chargement des lecteurs...
                        </div>
                    </div>
                </div>

                <div class="file-browser">
                    <div class="file-list" id="fileList">
                        <div class="loading">
                            <div class="spinner"></div>
                            Sélectionnez un lecteur pour commencer
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section d'analyse -->
            <div class="card analysis-card">
                <div class="analysis-header">
                    <h2>🔍 Analyse des Fichiers</h2>
                    <div style="text-align: center; margin-bottom: 15px;">
                        <button class="btn" onclick="openNativeFileDialog()" style="background: #28a745;">
                            📂 Sélectionner des fichiers .ec
                        </button>
                    </div>
                </div>

                <div class="selected-files" id="selectedFiles">
                    <p style="color: #6c757d; font-style: italic;">Aucun fichier .ec sélectionné</p>
                </div>

                <div class="analysis-options">
                    <div class="option">
                        <input type="checkbox" id="sqlAnalysis" checked>
                        <label>🗄️ Analyse SQL/ESQL</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="memoryAnalysis" checked>
                        <label>🧠 Analyse Mémoire</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="businessAnalysis" checked>
                        <label>📊 Logique Métier</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="performanceAnalysis" checked>
                        <label>⚡ Performance</label>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn" onclick="startAnalysis()" id="analyzeBtn" disabled>
                        🚀 Analyser les Fichiers Sélectionnés
                    </button>
                    <button class="btn btn-secondary" onclick="clearSelection()">
                        🗑️ Vider la Sélection
                    </button>
                </div>

                <div id="analysisStatus" class="status hidden"></div>

                <div class="results-section">
                    <h3>📊 Résultats</h3>
                    <div class="results-content" id="resultsContent">
                        Les résultats d'analyse apparaîtront ici...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPath = '';
        let selectedFiles = new Set();
        let isAnalyzing = false;

        // Initialisation
        window.onload = function() {
            loadDrives();
        };

        async function loadDrives() {
            try {
                const response = await fetch('/api/drives');
                const data = await response.json();

                if (response.ok) {
                    displayDrives(data.drives);
                } else {
                    showStatus('error', data.error || 'Erreur lors du chargement des lecteurs');
                }
            } catch (error) {
                showStatus('error', 'Erreur de connexion: ' + error.message);
            }
        }

        function displayDrives(drives) {
            const drivesGrid = document.getElementById('drivesGrid');
            drivesGrid.innerHTML = '';

            drives.forEach(drive => {
                const driveElement = document.createElement('div');
                driveElement.className = 'drive-item';
                driveElement.onclick = () => browseDirectory(drive.path);
                driveElement.innerHTML = `
                    <div class="drive-name">${drive.name}</div>
                    <div class="drive-size">${drive.size}</div>
                `;
                drivesGrid.appendChild(driveElement);
            });
        }

        async function browseDirectory(path) {
            try {
                showFileListLoading();

                const response = await fetch(`/api/browse?path=${encodeURIComponent(path)}`);
                const data = await response.json();

                if (response.ok) {
                    currentPath = data.current_path;
                    displayDirectoryContent(data);
                } else {
                    showStatus('error', data.error || 'Erreur lors du parcours du répertoire');
                    hideFileListLoading();
                }
            } catch (error) {
                showStatus('error', 'Erreur de connexion: ' + error.message);
                hideFileListLoading();
            }
        }

        function displayDirectoryContent(data) {
            document.getElementById('currentPath').textContent = data.current_path;

            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            if (data.items.length === 0) {
                fileList.innerHTML = '<div style="padding: 20px; text-align: center; color: #6c757d;">Dossier vide</div>';
                return;
            }

            data.items.forEach(item => {
                const fileElement = document.createElement('div');
                fileElement.className = 'file-item';

                if (item.is_ec) {
                    fileElement.classList.add('ec-file');
                }

                if (item.type === 'directory' || item.type === 'parent') {
                    fileElement.onclick = () => browseDirectory(item.path);
                    fileElement.style.cursor = 'pointer';
                } else if (item.is_ec) {
                    fileElement.onclick = () => toggleFileSelection(item.path, item.name, fileElement);
                }

                let checkbox = '';
                if (item.is_ec) {
                    const isSelected = selectedFiles.has(item.path);
                    checkbox = `<input type="checkbox" class="file-checkbox" ${isSelected ? 'checked' : ''} onclick="event.stopPropagation(); toggleFileSelection('${item.path}', '${item.name}', this.parentElement)">`;

                    if (isSelected) {
                        fileElement.classList.add('selected');
                    }
                }

                fileElement.innerHTML = `
                    <div class="file-icon">${item.icon || '📄'}</div>
                    <div class="file-info">
                        <div class="file-name">${item.name}</div>
                        <div class="file-details">${item.size} - ${item.modified}</div>
                    </div>
                    ${checkbox}
                `;

                fileList.appendChild(fileElement);
            });
        }

        function refreshCurrentDirectory() {
            if (currentPath) {
                browseDirectory(currentPath);
            }
        }

        function showFileListLoading() {
            document.getElementById('fileList').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Chargement du répertoire...
                </div>
            `;
        }

        function hideFileListLoading() {
            // La fonction displayDirectoryContent remplacera le contenu
        }

        // Fonction pour ouvrir le dialogue de sélection natif
        function openNativeFileDialog() {
            document.getElementById('nativeFileInput').click();
        }

        // Gestion de la sélection de fichiers native
        function handleNativeFileSelection(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            showStatus('info', `${files.length} fichier(s) sélectionné(s) via le dialogue natif`);

            // Ajouter les fichiers à la sélection
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.name.toLowerCase().endsWith('.ec')) {
                    // Pour les fichiers sélectionnés via le dialogue natif, on utilise le nom comme chemin
                    // car on n'a pas accès au chemin complet pour des raisons de sécurité
                    const filePath = file.webkitRelativePath || file.name;
                    selectedFiles.add(filePath);

                    // Ajouter à l'affichage de l'explorateur si pas déjà présent
                    addFileToExplorerDisplay(file);
                }
            }

            updateSelectedFilesDisplay();

            // Réinitialiser l'input pour permettre la re-sélection du même fichier
            event.target.value = '';
        }

        // Ajouter un fichier à l'affichage de l'explorateur
        function addFileToExplorerDisplay(file) {
            const fileList = document.getElementById('fileList');

            // Vérifier si le fichier n'est pas déjà affiché
            const existingItems = fileList.querySelectorAll('.file-item');
            for (let item of existingItems) {
                const fileName = item.querySelector('.file-name');
                if (fileName && fileName.textContent === file.name) {
                    // Fichier déjà affiché, juste le marquer comme sélectionné
                    item.classList.add('selected', 'ec-file');
                    const checkbox = item.querySelector('.file-checkbox');
                    if (checkbox) checkbox.checked = true;
                    return;
                }
            }

            // Créer un nouvel élément pour le fichier
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item ec-file selected';

            const filePath = file.webkitRelativePath || file.name;
            const fileSize = formatFileSize(file.size);
            const fileDate = new Date(file.lastModified).toLocaleString();

            fileElement.innerHTML = `
                <div class="file-icon">🔧</div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-details">${fileSize} - ${fileDate}</div>
                </div>
                <input type="checkbox" class="file-checkbox" checked onclick="event.stopPropagation(); toggleFileSelection('${filePath}', '${file.name}', this.parentElement)">
            `;

            fileElement.onclick = () => toggleFileSelection(filePath, file.name, fileElement);

            // Ajouter en haut de la liste (après le dossier parent s'il existe)
            const firstItem = fileList.querySelector('.file-item');
            if (firstItem && firstItem.querySelector('.file-name').textContent.includes('parent')) {
                fileList.insertBefore(fileElement, firstItem.nextSibling);
            } else {
                fileList.insertBefore(fileElement, firstItem);
            }
        }

        // Fonction utilitaire pour formater la taille des fichiers
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
        }





        function toggleFileSelection(filePath, fileName, element) {
            if (selectedFiles.has(filePath)) {
                selectedFiles.delete(filePath);
                element.classList.remove('selected');
                const checkbox = element.querySelector('.file-checkbox');
                if (checkbox) checkbox.checked = false;
            } else {
                selectedFiles.add(filePath);
                element.classList.add('selected');
                const checkbox = element.querySelector('.file-checkbox');
                if (checkbox) checkbox.checked = true;
            }

            updateSelectedFilesDisplay();
        }

        function updateSelectedFilesDisplay() {
            const selectedFilesDiv = document.getElementById('selectedFiles');
            const analyzeBtn = document.getElementById('analyzeBtn');

            if (selectedFiles.size === 0) {
                selectedFilesDiv.innerHTML = '<p style="color: #6c757d; font-style: italic;">Aucun fichier .ec sélectionné</p>';
                analyzeBtn.disabled = true;
            } else {
                let html = `<h4>${selectedFiles.size} fichier(s) sélectionné(s):</h4>`;
                selectedFiles.forEach(filePath => {
                    const fileName = filePath.split(/[\\\\/]/).pop();
                    html += `
                        <div class="selected-file">
                            <span>🔧 ${fileName}</span>
                            <button class="remove-file" onclick="removeFileFromSelection('${filePath}')">✕</button>
                        </div>
                    `;
                });
                selectedFilesDiv.innerHTML = html;
                analyzeBtn.disabled = isAnalyzing;
            }
        }

        function removeFileFromSelection(filePath) {
            selectedFiles.delete(filePath);
            updateSelectedFilesDisplay();

            // Mettre à jour l'affichage dans la liste des fichiers
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const checkbox = item.querySelector('.file-checkbox');
                if (checkbox && checkbox.onclick.toString().includes(filePath)) {
                    item.classList.remove('selected');
                    checkbox.checked = false;
                }
            });
        }

        function clearSelection() {
            selectedFiles.clear();
            updateSelectedFilesDisplay();

            // Décocher toutes les cases
            document.querySelectorAll('.file-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.parentElement.classList.remove('selected');
            });
        }

        async function startAnalysis() {
            if (selectedFiles.size === 0) {
                showStatus('error', 'Aucun fichier sélectionné');
                return;
            }

            if (isAnalyzing) {
                showStatus('info', 'Une analyse est déjà en cours');
                return;
            }

            const options = {
                sql_analysis: document.getElementById('sqlAnalysis').checked,
                memory_analysis: document.getElementById('memoryAnalysis').checked,
                business_analysis: document.getElementById('businessAnalysis').checked,
                performance_analysis: document.getElementById('performanceAnalysis').checked
            };

            try {
                isAnalyzing = true;
                document.getElementById('analyzeBtn').disabled = true;
                showStatus('info', 'Démarrage de l\\'analyse...');

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        files: Array.from(selectedFiles),
                        options: options
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showStatus('success', result.message);

                    // Démarrer le polling pour récupérer les résultats
                    pollForResults(result.analysis_id);
                } else {
                    showStatus('error', result.error);
                }
            } catch (error) {
                showStatus('error', 'Erreur: ' + error.message);
            } finally {
                isAnalyzing = false;
                updateSelectedFilesDisplay();
            }
        }

        // Fonction pour récupérer les résultats périodiquement
        async function pollForResults(analysisId, attempts = 0) {
            const maxAttempts = 30; // 30 tentatives = 30 secondes max

            if (attempts >= maxAttempts) {
                showStatus('error', 'Timeout: Impossible de récupérer les résultats');
                return;
            }

            try {
                const response = await fetch(`/api/results/${analysisId}`);

                if (response.ok) {
                    const data = await response.json();
                    displayAnalysisResults(data.results, data.statistics);
                    showStatus('success', 'Analyse terminée et résultats affichés!');
                } else if (response.status === 404) {
                    // Résultats pas encore prêts, réessayer dans 1 seconde
                    setTimeout(() => pollForResults(analysisId, attempts + 1), 1000);
                } else {
                    showStatus('error', 'Erreur lors de la récupération des résultats');
                }
            } catch (error) {
                // Réessayer en cas d'erreur réseau
                setTimeout(() => pollForResults(analysisId, attempts + 1), 1000);
            }
        }

        // Fonction pour afficher les résultats d'analyse
        function displayAnalysisResults(results, statistics) {
            const resultsContent = document.getElementById('resultsContent');

            let html = `📊 RÉSULTATS D'ANALYSE FRED2000
${'='.repeat(50)}

📅 Date: ${new Date().toLocaleString()}
📄 Fichiers analysés: ${statistics.total_files}
🚨 Total problèmes: ${statistics.total_issues}

📊 RÉPARTITION PAR GRAVITÉ:
🔴 Critique: ${statistics.severity_counts.critical || 0}
🟠 Élevé: ${statistics.severity_counts.high || 0}
🟡 Moyen: ${statistics.severity_counts.medium || 0}
🔵 Faible: ${statistics.severity_counts.low || 0}

📋 TYPES DE PROBLÈMES:
`;

            // Afficher les types de problèmes
            Object.entries(statistics.type_counts).forEach(([type, count]) => {
                html += `• ${type.replace('_', ' ').toUpperCase()}: ${count}\\n`;
            });

            html += `\\n${'='.repeat(50)}\\n📄 DÉTAILS PAR FICHIER:\\n${'='.repeat(50)}\\n\\n`;

            // Détails par fichier
            Object.entries(results).forEach(([fileName, result]) => {
                const issues = result.issues || [];
                const stats = result.stats || {};

                html += `📄 ${fileName}\\n`;
                html += `   Lignes: ${stats.total_lines || 'N/A'}\\n`;
                html += `   Fonctions: ${stats.total_functions || 'N/A'}\\n`;
                html += `   Problèmes: ${issues.length}\\n\\n`;

                if (issues.length > 0) {
                    html += `   🔍 Problèmes détectés:\\n`;
                    issues.slice(0, 5).forEach((issue, i) => {
                        const severityEmoji = {
                            'critical': '🔴',
                            'high': '🟠',
                            'medium': '🟡',
                            'low': '🔵'
                        }[issue.severity] || '⚪';

                        html += `   ${i+1}. ${severityEmoji} [${issue.severity.toUpperCase()}] Ligne ${issue.line}\\n`;
                        html += `      ${issue.description}\\n`;
                        html += `      Code: ${issue.code}\\n`;
                        html += `      💡 ${issue.suggestion}\\n\\n`;
                    });

                    if (issues.length > 5) {
                        html += `   ... et ${issues.length - 5} autres problèmes\\n\\n`;
                    }
                } else {
                    html += `   ✅ Aucun problème détecté!\\n\\n`;
                }

                html += `${'-'.repeat(40)}\\n\\n`;
            });

            // Recommandations
            html += `💡 RECOMMANDATIONS:\\n`;
            if (statistics.total_issues === 0) {
                html += `✅ Excellent! Aucun problème détecté.\\n`;
            } else if (statistics.total_issues < 10) {
                html += `🟢 Bon état général. Quelques améliorations mineures possibles.\\n`;
            } else if (statistics.total_issues < 50) {
                html += `🟡 État correct. Recommandé de corriger les problèmes de haute priorité.\\n`;
            } else {
                html += `🔴 Attention! Nombreux problèmes détectés. Révision approfondie recommandée.\\n`;
            }

            if (statistics.severity_counts.critical > 0) {
                html += `🚨 ${statistics.severity_counts.critical} problème(s) critique(s) à corriger en priorité!\\n`;
            }

            resultsContent.textContent = html;
        }





        function showStatus(type, message) {
            const statusDiv = document.getElementById('analysisStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.classList.remove('hidden');

            // Masquer automatiquement après 5 secondes pour les messages de succès
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusDiv.classList.add('hidden');
                }, 5000);
            }
        }
    </script>
</body>
</html>'''

def open_browser_delayed():
    """Ouvre le navigateur après un délai"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8081')
        print("🌐 Navigateur ouvert sur http://localhost:8081")
    except Exception as e:
        print(f"⚠ Impossible d'ouvrir le navigateur: {e}")

def main():
    """Fonction principale"""
    print("=" * 70)
    print("🔧 DEBUG AGENT - Explorateur de Fichiers Web")
    print("   Naviguez dans votre PC pour analyser vos fichiers .ec")
    print("   Version 1.0 - 2025-01-14")
    print("=" * 70)
    print()

    if not ANALYSIS_AVAILABLE:
        print("⚠ Modules d'analyse non disponibles")
        print("  L'explorateur fonctionnera mais l'analyse sera limitée")
        print()

    port = 8081

    # Ouvrir le navigateur dans un thread séparé
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        with socketserver.TCPServer(("", port), FileExplorerHandler) as httpd:
            print(f"🚀 Serveur démarré sur http://localhost:{port}")
            print("📁 Explorateur de fichiers disponible dans votre navigateur")
            print("⏹️  Appuyez sur Ctrl+C pour arrêter")
            print()
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Serveur arrêté")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
