#!/usr/bin/env python3
"""
Debug Agent
Agent de débogage spécialisé pour le système FRED2000 (fvbco020.ec)

Auteur: Assistant IA
Date: 2025-01-14
"""

import re
import os
import sys
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

class ErrorType(Enum):
    """Types d'erreurs détectables"""
    SQL_ERROR = "sql_error"
    MEMORY_LEAK = "memory_leak"
    BUFFER_OVERFLOW = "buffer_overflow"
    NULL_POINTER = "null_pointer"
    LOGIC_ERROR = "logic_error"
    BUSINESS_RULE = "business_rule"
    PERFORMANCE = "performance"
    SECURITY = "security"

class Severity(Enum):
    """Niveaux de gravité"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

@dataclass
class DebugIssue:
    """Représente un problème détecté"""
    error_type: ErrorType
    severity: Severity
    line_number: int
    function_name: str
    description: str
    code_snippet: str
    suggestion: str
    file_path: str = ""

class FRED2000DebugAgent:
    """Agent principal de débogage pour FRED2000"""
    
    def __init__(self, source_file: str = "fvbco020.ec"):
        self.source_file = source_file
        self.header_file = "fvbco020.h"
        self.issues: List[DebugIssue] = []
        self.code_lines: List[str] = []
        self.header_lines: List[str] = []
        self.functions: Dict[str, Tuple[int, int]] = {}  # nom_fonction: (ligne_debut, ligne_fin)
        
        # Patterns de détection d'erreurs
        self.sql_patterns = [
            r'EXEC\s+SQL\s+.*?;',
            r'sqlca\.sqlcode',
            r'DUPLICATE_KEY',
            r'TABLE_LOCKED'
        ]
        
        self.memory_patterns = [
            r'malloc\s*\(',
            r'calloc\s*\(',
            r'free\s*\(',
            r'strcpy\s*\(',
            r'strcat\s*\(',
            r'sprintf\s*\('
        ]
        
        self.load_source_files()
        self.analyze_functions()
    
    def load_source_files(self):
        """Charge les fichiers source"""
        try:
            if os.path.exists(self.source_file):
                with open(self.source_file, 'r', encoding='utf-8', errors='ignore') as f:
                    self.code_lines = f.readlines()
                print(f"✓ Fichier source chargé: {self.source_file} ({len(self.code_lines)} lignes)")
            else:
                print(f"⚠ Fichier source non trouvé: {self.source_file}")
                
            if os.path.exists(self.header_file):
                with open(self.header_file, 'r', encoding='utf-8', errors='ignore') as f:
                    self.header_lines = f.readlines()
                print(f"✓ Fichier header chargé: {self.header_file} ({len(self.header_lines)} lignes)")
            else:
                print(f"⚠ Fichier header non trouvé: {self.header_file}")
                
        except Exception as e:
            print(f"❌ Erreur lors du chargement des fichiers: {e}")
    
    def analyze_functions(self):
        """Analyse et identifie toutes les fonctions dans le code"""
        current_function = None
        brace_count = 0
        
        for i, line in enumerate(self.code_lines):
            line = line.strip()
            
            # Détection de début de fonction
            if re.match(r'^(int|void|char|long|double|static)\s+\w+\s*\([^)]*\)\s*$', line):
                if current_function:
                    self.functions[current_function] = (self.functions[current_function][0], i-1)
                
                func_match = re.search(r'(\w+)\s*\([^)]*\)', line)
                if func_match:
                    current_function = func_match.group(1)
                    self.functions[current_function] = (i+1, -1)
                    brace_count = 0
            
            # Comptage des accolades pour détecter la fin de fonction
            if current_function:
                brace_count += line.count('{') - line.count('}')
                if brace_count == 0 and '}' in line:
                    self.functions[current_function] = (self.functions[current_function][0], i+1)
                    current_function = None
        
        print(f"✓ {len(self.functions)} fonctions identifiées")
    
    def get_function_at_line(self, line_num: int) -> str:
        """Retourne le nom de la fonction à la ligne donnée"""
        for func_name, (start, end) in self.functions.items():
            if start <= line_num <= end:
                return func_name
        return "unknown"
    
    def analyze_sql_issues(self):
        """Détecte les problèmes SQL"""
        print("🔍 Analyse des problèmes SQL...")
        
        for i, line in enumerate(self.code_lines):
            line_num = i + 1
            
            # Détection des requêtes SQL sans gestion d'erreur
            if re.search(r'EXEC\s+SQL', line, re.IGNORECASE):
                # Vérifier si la ligne suivante gère sqlca.sqlcode
                next_lines = self.code_lines[i+1:i+5]  # Vérifier les 5 lignes suivantes
                has_error_handling = any(
                    'sqlca.sqlcode' in next_line or 'puterrdb' in next_line 
                    for next_line in next_lines
                )
                
                if not has_error_handling:
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.SQL_ERROR,
                        severity=Severity.HIGH,
                        line_number=line_num,
                        function_name=self.get_function_at_line(line_num),
                        description="Requête SQL sans gestion d'erreur appropriée",
                        code_snippet=line.strip(),
                        suggestion="Ajouter une vérification de sqlca.sqlcode après la requête",
                        file_path=self.source_file
                    ))
    
    def analyze_memory_issues(self):
        """Détecte les problèmes de gestion mémoire"""
        print("🔍 Analyse des problèmes de mémoire...")
        
        for i, line in enumerate(self.code_lines):
            line_num = i + 1
            
            # Détection d'utilisation de fonctions dangereuses
            if re.search(r'\bstrcpy\s*\(', line):
                self.issues.append(DebugIssue(
                    error_type=ErrorType.BUFFER_OVERFLOW,
                    severity=Severity.HIGH,
                    line_number=line_num,
                    function_name=self.get_function_at_line(line_num),
                    description="Utilisation de strcpy() - risque de buffer overflow",
                    code_snippet=line.strip(),
                    suggestion="Utiliser strncpy() ou memcpy() avec vérification de taille",
                    file_path=self.source_file
                ))
            
            if re.search(r'\bsprintf\s*\(', line):
                self.issues.append(DebugIssue(
                    error_type=ErrorType.BUFFER_OVERFLOW,
                    severity=Severity.MEDIUM,
                    line_number=line_num,
                    function_name=self.get_function_at_line(line_num),
                    description="Utilisation de sprintf() - risque de buffer overflow",
                    code_snippet=line.strip(),
                    suggestion="Utiliser snprintf() avec limitation de taille",
                    file_path=self.source_file
                ))
    
    def analyze_business_logic(self):
        """Détecte les problèmes de logique métier FRED2000"""
        print("🔍 Analyse de la logique métier...")
        
        for i, line in enumerate(self.code_lines):
            line_num = i + 1
            
            # Vérification des codes de retour
            if 'return ERROR' in line and 'if' not in line:
                # Vérifier si une erreur est loggée avant le return
                prev_lines = self.code_lines[max(0, i-3):i]
                has_logging = any(
                    'log_' in prev_line or 'puterrdb' in prev_line or 'op_fatal_msge' in prev_line
                    for prev_line in prev_lines
                )
                
                if not has_logging:
                    self.issues.append(DebugIssue(
                        error_type=ErrorType.LOGIC_ERROR,
                        severity=Severity.MEDIUM,
                        line_number=line_num,
                        function_name=self.get_function_at_line(line_num),
                        description="Return ERROR sans logging approprié",
                        code_snippet=line.strip(),
                        suggestion="Ajouter un message de log avant le return ERROR",
                        file_path=self.source_file
                    ))
    
    def run_full_analysis(self):
        """Lance une analyse complète"""
        print("🚀 Démarrage de l'analyse complète du code FRED2000...")
        print("=" * 60)
        
        self.issues.clear()
        
        # Lancer toutes les analyses
        self.analyze_sql_issues()
        self.analyze_memory_issues()
        self.analyze_business_logic()
        
        print(f"\n📊 Analyse terminée: {len(self.issues)} problèmes détectés")
        return self.issues
    
    def generate_report(self, output_file: str = "fred2000_debug_report.json"):
        """Génère un rapport de débogage"""
        report = {
            "source_file": self.source_file,
            "analysis_date": "2025-01-14",
            "total_issues": len(self.issues),
            "issues_by_severity": {
                "critical": len([i for i in self.issues if i.severity == Severity.CRITICAL]),
                "high": len([i for i in self.issues if i.severity == Severity.HIGH]),
                "medium": len([i for i in self.issues if i.severity == Severity.MEDIUM]),
                "low": len([i for i in self.issues if i.severity == Severity.LOW])
            },
            "issues": [
                {
                    "type": issue.error_type.value,
                    "severity": issue.severity.value,
                    "line": issue.line_number,
                    "function": issue.function_name,
                    "description": issue.description,
                    "code": issue.code_snippet,
                    "suggestion": issue.suggestion
                }
                for issue in self.issues
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Rapport généré: {output_file}")
        return output_file

if __name__ == "__main__":
    # Utilisation de l'agent
    agent = FRED2000DebugAgent()
    issues = agent.run_full_analysis()
    
    # Affichage des résultats
    if issues:
        print("\n🔍 PROBLÈMES DÉTECTÉS:")
        print("=" * 60)
        for issue in issues[:10]:  # Afficher les 10 premiers
            print(f"⚠ {issue.severity.value.upper()} - Ligne {issue.line_number}")
            print(f"   Fonction: {issue.function_name}")
            print(f"   Type: {issue.error_type.value}")
            print(f"   Description: {issue.description}")
            print(f"   Code: {issue.code_snippet}")
            print(f"   Suggestion: {issue.suggestion}")
            print("-" * 40)
    
    # Générer le rapport
    agent.generate_report()
