#!/usr/bin/env python3
"""
Script de lancement pour l'application web Debug Agent
<PERSON><PERSON><PERSON><PERSON> les dépendances et lance l'application

Auteur: Assistant IA
Date: 2025-01-14
"""

import sys
import os
import subprocess
import webbrowser
import time
import threading

def check_and_install_dependencies():
    """Vérifie et installe les dépendances nécessaires"""
    print("🔍 Vérification des dépendances...")
    
    required_packages = [
        'flask',
        'flask-cors', 
        'flask-socketio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 Installation des dépendances manquantes: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ])
            print("✅ Dépendances installées avec succès!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur lors de l'installation des dépendances: {e}")
            print("💡 Essayez d'exécuter manuellement: pip install -r requirements.txt")
            return False
    else:
        print("✅ Toutes les dépendances sont présentes")
    
    return True

def open_browser_delayed():
    """Ouvre le navigateur après un délai"""
    time.sleep(3)  # Attendre que le serveur démarre
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Navigateur ouvert sur http://localhost:5000")
    except Exception as e:
        print(f"⚠ Impossible d'ouvrir le navigateur automatiquement: {e}")
        print("📱 Ouvrez manuellement: http://localhost:5000")

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🔧 DEBUG AGENT - Application Web")
    print("   Interface Web Moderne pour l'Analyse des Fichiers .ec")
    print("   Version 1.0 - 2025-01-14")
    print("=" * 60)
    print()
    
    # Changer vers le dossier du script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 Dossier de travail: {os.getcwd()}")
    
    # Vérifier les dépendances
    if not check_and_install_dependencies():
        input("\nAppuyez sur Entrée pour fermer...")
        return 1
    
    # Vérifier que app.py existe
    if not os.path.exists('app.py'):
        print("❌ Fichier app.py non trouvé!")
        print("Assurez-vous que tous les fichiers sont dans le même dossier.")
        input("\nAppuyez sur Entrée pour fermer...")
        return 1
    
    print()
    print("🚀 Démarrage de l'application web...")
    print("📱 Interface disponible sur: http://localhost:5000")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
    print()
    
    # Ouvrir le navigateur dans un thread séparé
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # Importer et lancer l'application
        from app import app, socketio
        socketio.run(app, debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Arrêt demandé par l'utilisateur")
        return 0
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("Vérifiez que tous les modules sont présents.")
        input("\nAppuyez sur Entrée pour fermer...")
        return 1
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        input("\nAppuyez sur Entrée pour fermer...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
