#!/usr/bin/env python3
"""
Script de test pour l'Agent de Débogage FRED2000
Teste les fonctionnalités de base sans interface graphique

Auteur: Assistant IA
Date: 2025-01-14
"""

import os
import sys
import traceback

def test_imports():
    """Teste les imports des modules"""
    print("🔍 Test des imports...")
    
    try:
        from fred2000_debug_agent import FRED2000DebugAgent, ErrorType, Severity
        print("✅ fred2000_debug_agent importé")
    except Exception as e:
        print(f"❌ Erreur fred2000_debug_agent: {e}")
        return False
    
    try:
        from error_detectors import SQLErrorDetector, MemoryErrorDetector, BusinessLogicDetector, PerformanceDetector
        print("✅ error_detectors importé")
    except Exception as e:
        print(f"❌ Erreur error_detectors: {e}")
        return False
    
    try:
        from static_analyzer import FRED2000StaticAnalyzer
        print("✅ static_analyzer importé")
    except Exception as e:
        print(f"❌ Erreur static_analyzer: {e}")
        return False
    
    return True

def test_file_detection():
    """Teste la détection des fichiers .ec"""
    print("\n🔍 Test de détection des fichiers .ec...")
    
    import glob
    
    # Chercher dans le dossier actuel
    ec_files = glob.glob("*.ec")
    
    if ec_files:
        print(f"✅ {len(ec_files)} fichier(s) .ec trouvé(s):")
        for file in ec_files:
            size = os.path.getsize(file)
            print(f"   📄 {file} ({size} bytes)")
        return ec_files
    else:
        print("⚠ Aucun fichier .ec trouvé dans le dossier actuel")
        return []

def test_basic_analysis(ec_files):
    """Teste l'analyse de base"""
    if not ec_files:
        print("\n⚠ Pas de fichiers à analyser")
        return
    
    print(f"\n🔍 Test d'analyse sur {ec_files[0]}...")
    
    try:
        from fred2000_debug_agent import FRED2000DebugAgent
        
        # Créer l'agent
        agent = FRED2000DebugAgent(ec_files[0])
        print(f"✅ Agent créé pour {ec_files[0]}")
        print(f"   📊 {len(agent.code_lines)} lignes de code")
        print(f"   🔧 {len(agent.functions)} fonctions détectées")
        
        # Test d'analyse rapide
        print("🔍 Lancement d'une analyse rapide...")
        issues = agent.run_full_analysis()
        
        print(f"✅ Analyse terminée: {len(issues)} problèmes détectés")
        
        if issues:
            print("📋 Premiers problèmes détectés:")
            for i, issue in enumerate(issues[:3], 1):
                print(f"   {i}. Ligne {issue.line_number}: {issue.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        traceback.print_exc()
        return False

def test_gui_import():
    """Teste l'import de l'interface graphique"""
    print("\n🔍 Test de l'interface graphique...")
    
    try:
        import tkinter as tk
        print("✅ tkinter disponible")
        
        # Test de création d'une fenêtre simple
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre
        root.destroy()
        print("✅ tkinter fonctionne")
        
        from fred2000_gui import FRED2000GUI
        print("✅ fred2000_gui importé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur interface graphique: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Debug Agent - Tests de Fonctionnement")
    print("=" * 60)
    
    # Changer vers le dossier du script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 Dossier de travail: {os.getcwd()}")
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Imports
    if test_imports():
        success_count += 1
    
    # Test 2: Détection fichiers
    ec_files = test_file_detection()
    if ec_files:
        success_count += 1
    
    # Test 3: Analyse de base
    if test_basic_analysis(ec_files):
        success_count += 1
    
    # Test 4: Interface graphique
    if test_gui_import():
        success_count += 1
    
    # Résumé
    print("\n" + "=" * 60)
    print(f"📊 RÉSUMÉ DES TESTS: {success_count}/{total_tests} réussis")
    
    if success_count == total_tests:
        print("🎉 Tous les tests sont passés! L'agent est prêt à fonctionner.")
        print("\n💡 Pour lancer l'interface graphique:")
        print("   - Double-cliquez sur LANCER_FRED2000_DEBUG.bat")
        print("   - Ou exécutez: python launch_fred2000_debug.py")
    else:
        print("⚠ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    
    return success_count == total_tests

if __name__ == "__main__":
    try:
        success = main()
        
        print("\nAppuyez sur Entrée pour fermer...")
        input()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n👋 Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        traceback.print_exc()
        print("\nAppuyez sur Entrée pour fermer...")
        input()
        sys.exit(1)
